#!/usr/bin/env python3
"""
详细内存分析脚本
分析实际运行时的内存使用模式
"""

import torch
import time
import gc
from poison_main import Config
from utils.loaddata import load_entity_level_dataset, load_metadata
from darpatc import TriggerGenerator, AddTrigger
from model.autoencoder import build_model
from utils.config import build_args

def print_memory_usage(stage_name):
    """打印当前内存使用情况"""
    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated() / 1024**3
        cached = torch.cuda.memory_cached() / 1024**3
        reserved = torch.cuda.memory_reserved() / 1024**3
        print(f"{stage_name:25} | 已分配: {allocated:.3f}GB | 已缓存: {cached:.3f}GB | 已保留: {reserved:.3f}GB")
        return allocated
    else:
        print(f"{stage_name:25} | CPU模式 - 无GPU内存统计")
        return 0

def analyze_data_loading(cfg):
    """分析数据加载阶段的内存使用"""
    print("\n=== 数据加载内存分析 ===")
    print_memory_usage("初始状态")
    
    metadata = load_metadata(cfg.dataset)
    print_memory_usage("加载元数据后")
    
    # 加载一个训练图
    train_graph = load_entity_level_dataset(cfg.dataset, 'train', 0)
    if torch.cuda.is_available():
        train_graph = train_graph.to(cfg.device)
    print_memory_usage("加载训练图后")
    
    # 加载测试图
    test_graph = load_entity_level_dataset(cfg.dataset, 'test', 0)
    if torch.cuda.is_available():
        test_graph = test_graph.to(cfg.device)
    print_memory_usage("加载测试图后")
    
    print(f"\n图数据统计:")
    print(f"训练图: {train_graph.num_nodes():,} 节点, {train_graph.num_edges():,} 边")
    print(f"测试图: {test_graph.num_nodes():,} 节点, {test_graph.num_edges():,} 边")
    
    return train_graph, test_graph, metadata

def analyze_model_loading(cfg, metadata):
    """分析模型加载阶段的内存使用"""
    print("\n=== 模型加载内存分析 ===")
    
    # 构建检测器
    model_cfg = build_args()
    model_cfg.num_hidden = 64
    model_cfg.num_layers = 3
    model_cfg.n_dim = metadata['node_feature_dim']
    model_cfg.e_dim = metadata['edge_feature_dim']
    
    detector = build_model(model_cfg)
    if torch.cuda.is_available():
        detector = detector.to(cfg.device)
    print_memory_usage("加载检测器后")
    
    # 构建触发器生成器
    trigger_generator = TriggerGenerator(
        metadata['node_feature_dim'], 
        metadata['edge_feature_dim'], 
        cfg.trigger_shape
    )
    if torch.cuda.is_available():
        trigger_generator = trigger_generator.to(cfg.device)
    print_memory_usage("加载触发器生成器后")
    
    # 构建AddTrigger
    addtrigger = AddTrigger(cfg)
    if torch.cuda.is_available():
        addtrigger = addtrigger.to(cfg.device)
    print_memory_usage("加载AddTrigger后")
    
    # 创建优化器
    optimizer_d = torch.optim.Adam(detector.parameters(), lr=cfg.lr_d)
    optimizer_g = torch.optim.Adam(trigger_generator.parameters(), lr=cfg.lr_g)
    print_memory_usage("创建优化器后")
    
    return detector, trigger_generator, addtrigger, optimizer_d, optimizer_g

def analyze_forward_pass(cfg, train_graph, detector, trigger_generator):
    """分析前向传播的内存使用"""
    print("\n=== 前向传播内存分析 ===")
    
    # 检测器前向传播
    with torch.no_grad():
        loss, embeddings = detector(train_graph)
    print_memory_usage("检测器前向传播后")
    print(f"嵌入向量形状: {embeddings.shape}")
    
    # 触发器生成 (模拟子图)
    if train_graph.num_nodes() > 1000:
        # 创建一个小的子图用于测试
        from attack_utils import extract_subgraph
        subgraph, node_idx = extract_subgraph(train_graph, 0, [], [], k_hop=2)
        trigger = trigger_generator(subgraph, node_idx, temperature=0.5, hard=True)
        print_memory_usage("触发器生成后")
        print(f"触发器形状: {trigger.shape}")
    
    return embeddings

def analyze_loss_computation(cfg, embeddings):
    """分析损失计算的内存使用"""
    print("\n=== 损失计算内存分析 ===")
    
    # 模拟大规模嵌入计算
    num_nodes = embeddings.size(0)
    embedding_dim = embeddings.size(1)
    
    # 选择子集进行距离计算
    max_train = min(10000, num_nodes)
    max_test = min(1000, num_nodes)
    
    train_emb = embeddings[:max_train]
    test_emb = embeddings[:max_test]
    
    print(f"训练嵌入: {train_emb.shape}")
    print(f"测试嵌入: {test_emb.shape}")
    print_memory_usage("准备嵌入向量后")
    
    # 计算距离矩阵
    distances = torch.cdist(test_emb, train_emb, p=2)
    print_memory_usage("计算距离矩阵后")
    print(f"距离矩阵形状: {distances.shape}")
    
    # TopK计算
    topk_distances, topk_indices = torch.topk(distances, k=10, dim=1, largest=False)
    print_memory_usage("TopK计算后")
    
    # 损失计算
    loss = torch.mean(topk_distances)
    print_memory_usage("损失计算后")
    
    return loss

def analyze_batch_processing(cfg, train_graph, trigger_generator, addtrigger):
    """分析批处理的内存使用"""
    print("\n=== 批处理内存分析 ===")
    
    # 模拟批量触发器生成
    num_targets = min(100, train_graph.num_nodes() // 100)  # 模拟投毒节点
    target_nodes = list(range(0, num_targets * 100, 100))  # 间隔选择节点
    
    print(f"模拟 {len(target_nodes)} 个目标节点")
    print_memory_usage("开始批处理前")
    
    # 批量生成触发器
    triggers = []
    batch_size = cfg.trigger_batch_size
    
    for i in range(0, len(target_nodes), batch_size):
        batch_targets = target_nodes[i:i+batch_size]
        batch_triggers = []
        
        for node in batch_targets:
            try:
                from attack_utils import extract_subgraph
                subgraph, node_idx = extract_subgraph(train_graph, node, [], [], k_hop=cfg.k_hop)
                trigger = trigger_generator(subgraph, node_idx, temperature=0.5, hard=True)
                batch_triggers.append(trigger)
            except:
                # 如果节点无效，创建零触发器
                trigger = torch.zeros(cfg.trigger_shape, device=cfg.device)
                batch_triggers.append(trigger)
        
        if batch_triggers:
            batch_triggers = torch.stack(batch_triggers, dim=0)
            triggers.append(batch_triggers)
    
    if triggers:
        all_triggers = torch.cat(triggers, dim=0)
        print_memory_usage("批量触发器生成后")
        print(f"触发器批次形状: {all_triggers.shape}")
    
    return all_triggers if triggers else None

def run_memory_stress_test(cfg):
    """运行内存压力测试"""
    print("\n=== 内存压力测试 ===")
    
    # 模拟最大可能的内存使用
    max_nodes = 50000
    embedding_dim = 64
    
    print(f"创建大型嵌入向量: {max_nodes} x {embedding_dim}")
    large_embedding = torch.randn(max_nodes, embedding_dim, device=cfg.device)
    print_memory_usage("大型嵌入创建后")
    
    # 模拟距离计算
    test_size = 1000
    test_embedding = torch.randn(test_size, embedding_dim, device=cfg.device)
    
    print(f"计算距离矩阵: {test_size} x {max_nodes}")
    distances = torch.cdist(test_embedding, large_embedding, p=2)
    print_memory_usage("大型距离矩阵后")
    
    # 清理内存
    del large_embedding, test_embedding, distances
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    print_memory_usage("清理内存后")

def main():
    """主函数"""
    print("详细内存使用分析")
    print("=" * 80)
    
    cfg = Config()
    
    # 如果有CUDA，切换到CUDA
    if torch.cuda.is_available():
        cfg.device = torch.device("cuda")
        print(f"使用GPU: {torch.cuda.get_device_name()}")
        print(f"总显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    else:
        print("使用CPU模式")
    
    try:
        # 1. 数据加载分析
        train_graph, test_graph, metadata = analyze_data_loading(cfg)
        
        # 2. 模型加载分析
        detector, trigger_generator, addtrigger, optimizer_d, optimizer_g = analyze_model_loading(cfg, metadata)
        
        # 3. 前向传播分析
        embeddings = analyze_forward_pass(cfg, train_graph, detector, trigger_generator)
        
        # 4. 损失计算分析
        loss = analyze_loss_computation(cfg, embeddings)
        
        # 5. 批处理分析
        triggers = analyze_batch_processing(cfg, train_graph, trigger_generator, addtrigger)
        
        # 6. 内存压力测试
        if torch.cuda.is_available():
            run_memory_stress_test(cfg)
        
        print("\n=== 最终内存统计 ===")
        final_memory = print_memory_usage("分析完成")
        
        if torch.cuda.is_available():
            total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            usage_percent = (final_memory / total_memory) * 100
            print(f"显存使用率: {usage_percent:.1f}%")
            
            if usage_percent < 50:
                print("✅ 显存使用良好，可以增加批处理大小")
            elif usage_percent < 80:
                print("⚠️  显存使用适中，建议保持当前设置")
            else:
                print("❌ 显存使用过高，建议减少批处理大小")
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        print("\n内存分析完成!")

if __name__ == "__main__":
    main()
