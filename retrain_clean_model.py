#!/usr/bin/env python3
"""
使用干净数据重新训练模型
"""

import torch
import os
import shutil
from tqdm import tqdm
from utils.config import build_args
from utils.loaddata import load_metadata, load_entity_level_dataset
from model.autoencoder import build_model
from utils.utils import create_optimizer

def backup_poisoned_data():
    """备份投毒数据"""
    print("=== 备份投毒数据 ===")
    
    metadata = load_metadata('theia')
    backup_dir = './data/theia/poisoned_backup'
    
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
        print(f"创建备份目录: {backup_dir}")
    
    # 备份投毒的训练数据
    for i in range(metadata['n_train']):
        poisoned_file = f'./data/theia/train{i}_poisoned.pkl'
        backup_file = f'{backup_dir}/train{i}_poisoned.pkl'
        
        if os.path.exists(poisoned_file):
            shutil.copy2(poisoned_file, backup_file)
            print(f"备份: train{i}_poisoned.pkl")
    
    print("投毒数据备份完成")

def train_clean_model():
    """使用干净数据训练模型"""
    print("\n=== 使用干净数据训练模型 ===")
    
    # 设备设置
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 加载参数和元数据
    args = build_args()
    dataset_name = args.dataset
    metadata = load_metadata(dataset_name)
    
    # 设置模型参数
    args.n_dim = metadata['node_feature_dim']
    args.e_dim = metadata['edge_feature_dim']
    args.num_hidden = 64
    args.num_layers = 3
    args.max_epoch = 50  # 减少训练轮数用于测试
    
    print(f"数据集: {dataset_name}")
    print(f"节点特征维度: {args.n_dim}")
    print(f"边特征维度: {args.e_dim}")
    print(f"训练轮数: {args.max_epoch}")
    
    # 构建模型
    model = build_model(args)
    model = model.to(device)
    model.train()
    
    # 创建优化器
    optimizer = create_optimizer(args.optimizer, model, args.lr, args.weight_decay)
    
    print("模型和优化器创建完成")
    
    # 训练循环
    n_train = metadata['n_train']
    epoch_iter = tqdm(range(args.max_epoch))
    
    for epoch in epoch_iter:
        epoch_loss = 0
        
        for i in range(n_train):
            # 加载干净的训练数据
            g = load_entity_level_dataset(dataset_name, 'train', i).to(device)
            
            # 前向传播
            loss, _ = model(g)
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            epoch_loss += loss.item()
        
        avg_loss = epoch_loss / n_train
        epoch_iter.set_description(f'Epoch {epoch}, Loss: {avg_loss:.6f}')
        
        # 每10个epoch保存一次模型
        if (epoch + 1) % 10 == 0:
            checkpoint_path = f'./checkpoints/clean_checkpoint_epoch_{epoch+1}.pt'
            torch.save(model.state_dict(), checkpoint_path)
            print(f"\n保存检查点: {checkpoint_path}")
    
    # 保存最终模型
    final_checkpoint = './checkpoints/clean_checkpoint_final.pt'
    torch.save(model.state_dict(), final_checkpoint)
    print(f"\n训练完成，最终模型保存到: {final_checkpoint}")
    
    return model

def test_clean_model():
    """测试干净模型的效果"""
    print("\n=== 测试干净模型的效果 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 加载干净模型
    args = build_args()
    metadata = load_metadata(args.dataset)
    args.n_dim = metadata['node_feature_dim']
    args.e_dim = metadata['edge_feature_dim']
    args.num_hidden = 64
    args.num_layers = 3
    
    clean_model = build_model(args)
    clean_model.load_state_dict(torch.load('./checkpoints/clean_checkpoint_final.pt', map_location=device))
    clean_model = clean_model.to(device)
    clean_model.eval()
    
    # 加载投毒模型进行比较
    poisoned_model = build_model(args)
    poisoned_model.load_state_dict(torch.load(f"./checkpoints/checkpoint-{args.dataset}.pt", map_location=device))
    poisoned_model = poisoned_model.to(device)
    poisoned_model.eval()
    
    # 加载测试数据
    test_g = load_entity_level_dataset('theia', 'test', 0).to(device)
    
    # 获取恶意节点
    from poison_main import Config
    from attack_utils import get_map, get_mal_node_msg
    from attack import poison_data
    from darpatc import TriggerGenerator
    
    cfg = Config()
    cfg.device = device
    cfg.n_dim = metadata['node_feature_dim']
    cfg.e_dim = metadata['edge_feature_dim']
    cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)
    malicious_node, mal_file_msg, mal_socket_msg = get_mal_node_msg(cfg, test_g)
    
    # 生成投毒测试图
    trigger_generator = TriggerGenerator(cfg.n_dim, cfg.e_dim, cfg.trigger_shape)
    trigger_generator.load_state_dict(torch.load('./poison_model/trigger_generator48.pth', map_location=device))
    trigger_generator = trigger_generator.to(device)
    trigger_generator.eval()
    
    poisoned_test_g = poison_data(cfg, trigger_generator, test_g, 
                                malicious_node['SUBJECT_PROCESS'], 
                                mal_socket_msg, mal_file_msg)
    
    # 计算嵌入
    with torch.no_grad():
        # 干净模型
        clean_model_clean_emb = clean_model.embed(test_g).cpu().numpy()
        clean_model_poisoned_emb = clean_model.embed(poisoned_test_g).cpu().numpy()
        
        # 投毒模型
        poisoned_model_clean_emb = poisoned_model.embed(test_g).cpu().numpy()
        poisoned_model_poisoned_emb = poisoned_model.embed(poisoned_test_g).cpu().numpy()
    
    # 获取恶意节点索引
    all_mal_nodes = []
    for nodes in malicious_node.values():
        all_mal_nodes.extend(nodes)
    
    # 分析结果
    print("嵌入差异分析:")
    
    # 干净模型的嵌入差异
    clean_model_diff = ((clean_model_clean_emb - clean_model_poisoned_emb) ** 2).sum(axis=1)
    clean_model_mal_diff = clean_model_diff[all_mal_nodes]
    
    # 投毒模型的嵌入差异
    poisoned_model_diff = ((poisoned_model_clean_emb - poisoned_model_poisoned_emb) ** 2).sum(axis=1)
    poisoned_model_mal_diff = poisoned_model_diff[all_mal_nodes]
    
    print(f"干净模型 - 恶意节点嵌入差异: {clean_model_mal_diff.mean():.6f}")
    print(f"投毒模型 - 恶意节点嵌入差异: {poisoned_model_mal_diff.mean():.6f}")
    
    if clean_model_mal_diff.mean() > poisoned_model_mal_diff.mean():
        print("✅ 干净模型显示出更大的嵌入差异，这是预期的结果")
    else:
        print("⚠️  干净模型的嵌入差异仍然很小，可能需要进一步调试")
    
    # 计算与训练集的距离变化
    from sklearn.neighbors import NearestNeighbors
    import numpy as np
    
    # 加载干净训练数据的嵌入
    train_embeddings = []
    for i in range(metadata['n_train']):
        train_g = load_entity_level_dataset('theia', 'train', i).to(device)
        with torch.no_grad():
            train_emb = clean_model.embed(train_g).cpu().numpy()
            train_embeddings.append(train_emb)
    
    train_embeddings = np.concatenate(train_embeddings, axis=0)
    
    # 标准化
    train_mean = train_embeddings.mean(axis=0)
    train_std = train_embeddings.std(axis=0)
    
    train_norm = (train_embeddings - train_mean) / train_std
    clean_norm = (clean_model_clean_emb - train_mean) / train_std
    poisoned_norm = (clean_model_poisoned_emb - train_mean) / train_std
    
    # 计算距离
    nbrs = NearestNeighbors(n_neighbors=10, n_jobs=-1)
    nbrs.fit(train_norm)
    
    clean_distances, _ = nbrs.kneighbors(clean_norm[all_mal_nodes])
    poisoned_distances, _ = nbrs.kneighbors(poisoned_norm[all_mal_nodes])
    
    print(f"\n距离分析 (干净模型):")
    print(f"干净恶意节点平均距离: {clean_distances.mean():.4f}")
    print(f"投毒恶意节点平均距离: {poisoned_distances.mean():.4f}")
    print(f"距离变化: {poisoned_distances.mean() - clean_distances.mean():.4f}")
    
    if poisoned_distances.mean() < clean_distances.mean():
        print("✅ 投毒后距离减小，后门攻击可能有效")
    else:
        print("⚠️  投毒后距离增大或不变，后门攻击可能无效")

def main():
    """主函数"""
    print("使用干净数据重新训练模型")
    print("=" * 50)
    
    # 备份投毒数据
    backup_poisoned_data()
    
    # 训练干净模型
    clean_model = train_clean_model()
    
    # 测试干净模型
    test_clean_model()
    
    print("\n" + "=" * 50)
    print("重新训练完成!")
    
    print("\n📋 下一步:")
    print("1. 使用干净模型重新运行eval.py")
    print("2. 比较干净模型和投毒模型的检测效果")
    print("3. 验证后门攻击是否按预期工作")

if __name__ == "__main__":
    main()
