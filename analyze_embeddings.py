#!/usr/bin/env python3
"""
分析嵌入向量和节点相似性
"""

import os
import torch
import numpy as np
import pickle as pkl
import matplotlib.pyplot as plt
from sklearn.neighbors import NearestNeighbors
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
import seaborn as sns
from tqdm import tqdm
import json

from utils.config import build_args
from utils.loaddata import load_metadata, load_entity_level_dataset
from model.autoencoder import build_model
from poison_main import Config
from attack_utils import get_map, get_mal_node_msg
from attack import poison_data
from darpatc import TriggerGenerator

def load_model_and_data():
    """加载模型和数据"""
    print("=== 加载模型和数据 ===")
    
    # 设备设置
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 加载参数和元数据
    args = build_args()
    dataset_name = args.dataset
    metadata = load_metadata(dataset_name)
    
    # 加载节点类型字典
    with open(f'./data/{dataset_name}/node_type_dict.json', 'r', encoding='utf-8') as f:
        node_type_dict = json.load(f)
    node_type_dict_reverse = {v: k for k, v in node_type_dict.items()}
    
    # 设置模型参数
    args.n_dim = metadata['node_feature_dim']
    args.e_dim = metadata['edge_feature_dim']
    args.num_hidden = 64
    args.num_layers = 3
    
    # 加载模型
    model = build_model(args)
    model.load_state_dict(torch.load(f"./checkpoints/checkpoint-{dataset_name}.pt", map_location=device))
    model = model.to(device)
    model.eval()
    print("模型加载成功")
    
    # 加载测试图
    g = load_entity_level_dataset(dataset_name, 'test', 0).to(device)
    print(f"测试图: {g.num_nodes()} 节点, {g.num_edges()} 边")
    
    # 加载训练图嵌入
    train_embeddings = []
    for i in range(metadata['n_train']):
        train_g = load_entity_level_dataset(dataset_name, 'train', i).to(device)
        with torch.no_grad():
            train_embeddings.append(model.embed(train_g).cpu().numpy())
    train_embeddings = np.concatenate(train_embeddings, axis=0)
    print(f"训练嵌入: {train_embeddings.shape}")
    
    # 获取恶意节点
    cfg = Config()
    cfg.device = device
    cfg.n_dim = metadata['node_feature_dim']
    cfg.e_dim = metadata['edge_feature_dim']
    cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)
    malicious_node, mal_file_msg, mal_socket_msg = get_mal_node_msg(cfg, g)
    print(f"恶意节点数量: {len(malicious_node['SUBJECT_PROCESS'])}")
    
    # 获取测试图嵌入
    with torch.no_grad():
        test_embeddings = model.embed(g).cpu().numpy()
    print(f"测试嵌入: {test_embeddings.shape}")
    
    # 获取投毒图嵌入
    trigger_generator = TriggerGenerator(cfg.n_dim, cfg.e_dim, cfg.trigger_shape)
    try:
        trigger_generator.load_state_dict(torch.load('./poison_model/trigger_generator48.pth', map_location=device))
        trigger_generator = trigger_generator.to(device)
        trigger_generator.eval()
        g_p = poison_data(cfg, trigger_generator, g, malicious_node['SUBJECT_PROCESS'], mal_socket_msg, mal_file_msg)
        with torch.no_grad():
            poisoned_embeddings = model.embed(g_p).cpu().numpy()
        print(f"投毒图嵌入: {poisoned_embeddings.shape}")
    except FileNotFoundError:
        print("触发器生成器文件不存在，跳过投毒嵌入计算")
        poisoned_embeddings = None
    
    return {
        'device': device,
        'model': model,
        'test_graph': g,
        'train_embeddings': train_embeddings,
        'test_embeddings': test_embeddings,
        'poisoned_embeddings': poisoned_embeddings,
        'malicious_node': malicious_node,
        'node_type_dict': node_type_dict,
        'node_type_dict_reverse': node_type_dict_reverse,
        'metadata': metadata,
        'dataset_name': dataset_name
    }

def analyze_node_similarity(data):
    """分析节点相似性"""
    print("\n=== 分析节点相似性 ===")
    
    train_embeddings = data['train_embeddings']
    test_embeddings = data['test_embeddings']
    malicious_node = data['malicious_node']
    node_type_dict_reverse = data['node_type_dict_reverse']
    
    # 标准化嵌入
    train_mean = train_embeddings.mean(axis=0)
    train_std = train_embeddings.std(axis=0)
    train_embeddings_norm = (train_embeddings - train_mean) / train_std
    test_embeddings_norm = (test_embeddings - train_mean) / train_std
    
    # 获取恶意节点嵌入
    mal_idx = []
    for _, v in malicious_node.items():
        mal_idx.extend(v)
    mal_embeddings = test_embeddings_norm[mal_idx]
    print(f"恶意节点嵌入: {mal_embeddings.shape}")
    
    # 计算恶意节点与训练集的距离
    n_neighbors = 10
    nbrs = NearestNeighbors(n_neighbors=n_neighbors, n_jobs=-1)
    nbrs.fit(train_embeddings_norm)
    
    # 计算恶意节点的最近邻
    distances, indices = nbrs.kneighbors(mal_embeddings, n_neighbors=n_neighbors)
    
    # 分析最近邻的节点类型
    print("\n恶意节点的最近邻节点类型分布:")
    neighbor_types = []
    for i, (dist, idx) in enumerate(zip(distances, indices)):
        print(f"\n恶意节点 {i} (类型: {node_type_dict_reverse[data['test_graph'].ndata['type'][mal_idx[i]].item()]}):")
        for j, (d, neighbor_idx) in enumerate(zip(dist, idx)):
            # 获取训练集中邻居节点的类型
            # 注意：这里假设训练集的节点类型信息可以通过加载训练图获取
            # 如果不可行，可能需要修改此部分
            print(f"  邻居 {j}: 距离 = {d:.4f}")
        neighbor_types.extend([node_type_dict_reverse.get(1, "Unknown") for _ in idx])
    
    type_counts = {}
    for t in neighbor_types:
        type_counts[t] = type_counts.get(t, 0) + 1
    
    print("\n最近邻节点类型统计:")
    for t, count in sorted(type_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"  {t}: {count} ({count/len(neighbor_types)*100:.1f}%)")
    
    return {
        'distances': distances,
        'indices': indices,
        'mal_idx': mal_idx,
        'train_embeddings_norm': train_embeddings_norm,
        'test_embeddings_norm': test_embeddings_norm,
        'mal_embeddings': mal_embeddings
    }

def visualize_embeddings(data, similarity_data):
    """可视化嵌入向量"""
    print("\n=== 可视化嵌入向量 ===")
    
    # 准备数据
    train_embeddings = similarity_data['train_embeddings_norm']
    test_embeddings = similarity_data['test_embeddings_norm']
    mal_idx = similarity_data['mal_idx']
    
    # 采样一部分训练嵌入以加快可视化
    np.random.seed(42)
    sample_size = min(5000, len(train_embeddings))
    sample_indices = np.random.choice(len(train_embeddings), sample_size, replace=False)
    train_sample = train_embeddings[sample_indices]
    
    # 获取恶意节点的嵌入
    mal_embeddings = test_embeddings[mal_idx]
    
    # 合并数据进行降维
    combined = np.vstack([train_sample, mal_embeddings])
    
    # 使用PCA进行降维
    print("执行PCA降维...")
    pca = PCA(n_components=2)
    combined_pca = pca.fit_transform(combined)
    
    # 分离结果
    train_pca = combined_pca[:sample_size]
    mal_pca = combined_pca[sample_size:]
    
    # 绘制PCA结果
    plt.figure(figsize=(10, 8))
    plt.scatter(train_pca[:, 0], train_pca[:, 1], c='blue', alpha=0.5, s=5, label='训练节点')
    plt.scatter(mal_pca[:, 0], mal_pca[:, 1], c='red', s=20, label='恶意节点')
    plt.title('PCA降维可视化')
    plt.legend()
    plt.savefig('pca_visualization.png')
    print("PCA可视化已保存到 pca_visualization.png")
    
    # 使用t-SNE进行降维
    print("执行t-SNE降维...")
    tsne = TSNE(n_components=2, random_state=42)
    combined_tsne = tsne.fit_transform(combined)
    
    # 分离结果
    train_tsne = combined_tsne[:sample_size]
    mal_tsne = combined_tsne[sample_size:]
    
    # 绘制t-SNE结果
    plt.figure(figsize=(10, 8))
    plt.scatter(train_tsne[:, 0], train_tsne[:, 1], c='blue', alpha=0.5, s=5, label='训练节点')
    plt.scatter(mal_tsne[:, 0], mal_tsne[:, 1], c='red', s=20, label='恶意节点')
    plt.title('t-SNE降维可视化')
    plt.legend()
    plt.savefig('tsne_visualization.png')
    print("t-SNE可视化已保存到 tsne_visualization.png")

def analyze_neighborhood(data, similarity_data):
    """分析恶意节点的邻域结构"""
    print("\n=== 分析恶意节点的邻域结构 ===")
    
    test_graph = data['test_graph']
    mal_idx = similarity_data['mal_idx']
    
    # 分析每个恶意节点的邻域
    for i, node_idx in enumerate(mal_idx):
        print(f"\n恶意节点 {i} (ID: {node_idx}):")
        
        # 获取节点类型
        node_type = test_graph.ndata['type'][node_idx].item()
        node_type_name = data['node_type_dict_reverse'].get(node_type, "Unknown")
        print(f"  类型: {node_type_name}")
        
        # 获取邻居节点
        neighbors = []
        for j, edge in enumerate(test_graph.edges()):
            if edge[0].item() == node_idx:
                neighbors.append(edge[1].item())
            elif edge[1].item() == node_idx:
                neighbors.append(edge[0].item())
        
        print(f"  邻居数量: {len(neighbors)}")
        
        # 分析邻居节点类型
        if neighbors:
            neighbor_types = test_graph.ndata['type'][neighbors].cpu().numpy()
            type_counts = {}
            for t in neighbor_types:
                type_name = data['node_type_dict_reverse'].get(t, "Unknown")
                type_counts[type_name] = type_counts.get(type_name, 0) + 1
            
            print("  邻居节点类型分布:")
            for t, count in sorted(type_counts.items(), key=lambda x: x[1], reverse=True):
                print(f"    {t}: {count} ({count/len(neighbors)*100:.1f}%)")

def compare_clean_vs_poisoned(data, similarity_data):
    """比较干净和投毒后的嵌入"""
    print("\n=== 比较干净和投毒后的嵌入 ===")
    
    if data['poisoned_embeddings'] is None:
        print("没有投毒嵌入数据，跳过比较")
        return
    
    train_embeddings = data['train_embeddings']
    test_embeddings = data['test_embeddings']
    poisoned_embeddings = data['poisoned_embeddings']
    mal_idx = similarity_data['mal_idx']
    
    # 标准化嵌入
    train_mean = train_embeddings.mean(axis=0)
    train_std = train_embeddings.std(axis=0)
    train_embeddings_norm = (train_embeddings - train_mean) / train_std
    test_embeddings_norm = (test_embeddings - train_mean) / train_std
    poisoned_embeddings_norm = (poisoned_embeddings - train_mean) / train_std
    
    # 获取干净和投毒后的恶意节点嵌入
    clean_mal_embeddings = test_embeddings_norm[mal_idx]
    poisoned_mal_embeddings = poisoned_embeddings_norm[mal_idx]
    
    # 计算与训练集的距离
    n_neighbors = 10
    nbrs = NearestNeighbors(n_neighbors=n_neighbors, n_jobs=-1)
    nbrs.fit(train_embeddings_norm)
    
    # 计算干净恶意节点的最近邻
    clean_distances, clean_indices = nbrs.kneighbors(clean_mal_embeddings, n_neighbors=n_neighbors)
    
    # 计算投毒后恶意节点的最近邻
    poisoned_distances, poisoned_indices = nbrs.kneighbors(poisoned_mal_embeddings, n_neighbors=n_neighbors)
    
    # 比较距离
    print("\n干净vs投毒后的平均距离:")
    print(f"  干净恶意节点平均距离: {clean_distances.mean():.4f}")
    print(f"  投毒后恶意节点平均距离: {poisoned_distances.mean():.4f}")
    
    # 计算干净和投毒后嵌入之间的距离
    embedding_distances = np.sqrt(((clean_mal_embeddings - poisoned_mal_embeddings) ** 2).sum(axis=1))
    print(f"  干净和投毒后嵌入之间的平均距离: {embedding_distances.mean():.4f}")
    
    # 绘制距离分布
    plt.figure(figsize=(12, 6))
    
    plt.subplot(1, 2, 1)
    plt.hist(clean_distances.mean(axis=1), bins=20, alpha=0.5, label='干净')
    plt.hist(poisoned_distances.mean(axis=1), bins=20, alpha=0.5, label='投毒后')
    plt.xlabel('平均距离')
    plt.ylabel('频率')
    plt.title('恶意节点到训练集的平均距离分布')
    plt.legend()
    
    plt.subplot(1, 2, 2)
    plt.hist(embedding_distances, bins=20)
    plt.xlabel('距离')
    plt.ylabel('频率')
    plt.title('干净vs投毒后嵌入的距离分布')
    
    plt.tight_layout()
    plt.savefig('distance_comparison.png')
    print("距离比较已保存到 distance_comparison.png")

def main():
    """主函数"""
    print("嵌入分析开始")
    print("=" * 50)
    
    # 加载数据
    data = load_model_and_data()
    
    # 分析节点相似性
    similarity_data = analyze_node_similarity(data)
    
    # 可视化嵌入
    visualize_embeddings(data, similarity_data)
    
    # 分析邻域结构
    analyze_neighborhood(data, similarity_data)
    
    # 比较干净和投毒后的嵌入
    compare_clean_vs_poisoned(data, similarity_data)
    
    print("\n" + "=" * 50)
    print("分析完成!")

if __name__ == "__main__":
    main()
