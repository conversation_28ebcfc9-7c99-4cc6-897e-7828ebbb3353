#!/usr/bin/env python3
"""
调试邻居计算逻辑
"""

import torch
import numpy as np
import json
from utils.loaddata import load_entity_level_dataset
from poison_main import Config
from attack_utils import get_map, get_mal_node_msg

def debug_neighbor_calculation():
    """调试邻居计算逻辑"""
    print("=== 调试邻居计算逻辑 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    g = load_entity_level_dataset('theia', 'test', 0).to(device)
    
    print(f"图信息: {g.num_nodes()} 节点, {g.num_edges()} 边")
    print(f"图设备: {g.device}")
    
    # 获取恶意节点
    cfg = Config()
    cfg.device = device
    metadata = {'node_feature_dim': 5, 'edge_feature_dim': 17}
    cfg.n_dim = metadata['node_feature_dim']
    cfg.e_dim = metadata['edge_feature_dim']
    cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)
    malicious_node, mal_file_msg, mal_socket_msg = get_mal_node_msg(cfg, g)
    
    # 选择几个恶意节点进行详细分析
    test_nodes = []
    for node_type, nodes in malicious_node.items():
        if nodes:
            test_nodes.append((node_type, nodes[0]))  # 取第一个节点
            if len(test_nodes) >= 3:  # 只测试3个节点
                break
    
    print(f"\n测试节点: {test_nodes}")
    
    # 方法1: 使用DGL的edges()函数
    print("\n=== 方法1: 使用DGL edges() ===")
    src, dst = g.edges()
    print(f"边数组形状: src={src.shape}, dst={dst.shape}")
    print(f"边数组设备: src={src.device}, dst={dst.device}")
    
    for node_type, node_id in test_nodes:
        print(f"\n节点 {node_id} ({node_type}):")
        
        # 找出邻居
        out_neighbors = dst[src == node_id]
        in_neighbors = src[dst == node_id]
        all_neighbors = torch.cat([out_neighbors, in_neighbors])
        unique_neighbors = torch.unique(all_neighbors)
        
        print(f"  出邻居: {len(out_neighbors)} 个")
        print(f"  入邻居: {len(in_neighbors)} 个")
        print(f"  总邻居: {len(unique_neighbors)} 个")
        
        if len(unique_neighbors) > 0:
            print(f"  前5个邻居: {unique_neighbors[:5].cpu().tolist()}")
    
    # 方法2: 使用循环遍历边
    print("\n=== 方法2: 循环遍历边 ===")
    for node_type, node_id in test_nodes:
        print(f"\n节点 {node_id} ({node_type}):")
        
        neighbors = []
        for i, (s, d) in enumerate(zip(src, dst)):
            if s.item() == node_id:
                neighbors.append(d.item())
            if d.item() == node_id:
                neighbors.append(s.item())
        
        unique_neighbors = list(set(neighbors))
        print(f"  邻居数量: {len(unique_neighbors)}")
        
        if len(unique_neighbors) > 0:
            print(f"  前5个邻居: {unique_neighbors[:5]}")
    
    # 方法3: 使用DGL的in_edges和out_edges
    print("\n=== 方法3: 使用DGL in_edges/out_edges ===")
    for node_type, node_id in test_nodes:
        print(f"\n节点 {node_id} ({node_type}):")
        
        try:
            # 获取入边和出边
            in_edges = g.in_edges(node_id)
            out_edges = g.out_edges(node_id)
            
            print(f"  入边数: {len(in_edges[0])}")
            print(f"  出边数: {len(out_edges[0])}")
            
            # 获取邻居
            in_neighbors = in_edges[0]  # 源节点
            out_neighbors = out_edges[1]  # 目标节点
            all_neighbors = torch.cat([in_neighbors, out_neighbors])
            unique_neighbors = torch.unique(all_neighbors)
            
            print(f"  总邻居: {len(unique_neighbors)} 个")
            
            if len(unique_neighbors) > 0:
                print(f"  前5个邻居: {unique_neighbors[:5].cpu().tolist()}")
                
        except Exception as e:
            print(f"  错误: {e}")
    
    # 方法4: 检查节点是否真的存在于边中
    print("\n=== 方法4: 检查节点存在性 ===")
    for node_type, node_id in test_nodes:
        print(f"\n节点 {node_id} ({node_type}):")
        
        # 检查节点是否出现在边中
        appears_as_src = (src == node_id).sum().item()
        appears_as_dst = (dst == node_id).sum().item()
        
        print(f"  作为源节点出现: {appears_as_src} 次")
        print(f"  作为目标节点出现: {appears_as_dst} 次")
        print(f"  总出现次数: {appears_as_src + appears_as_dst}")
        
        # 检查节点类型
        node_type_id = g.ndata['type'][node_id].item()
        print(f"  节点类型ID: {node_type_id}")
        
        # 检查节点特征
        node_attr = g.ndata['attr'][node_id]
        print(f"  节点特征形状: {node_attr.shape}")
        print(f"  节点特征非零元素: {(node_attr != 0).sum().item()}")

def check_graph_consistency():
    """检查图的一致性"""
    print("\n=== 检查图的一致性 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    g = load_entity_level_dataset('theia', 'test', 0).to(device)
    
    # 检查边的有效性
    src, dst = g.edges()
    max_node_id = g.num_nodes() - 1
    
    invalid_src = (src > max_node_id).sum().item()
    invalid_dst = (dst > max_node_id).sum().item()
    negative_src = (src < 0).sum().item()
    negative_dst = (dst < 0).sum().item()
    
    print(f"无效源节点数: {invalid_src}")
    print(f"无效目标节点数: {invalid_dst}")
    print(f"负数源节点数: {negative_src}")
    print(f"负数目标节点数: {negative_dst}")
    
    # 检查自环
    self_loops = (src == dst).sum().item()
    print(f"自环数: {self_loops}")
    
    # 检查节点覆盖率
    unique_src = torch.unique(src)
    unique_dst = torch.unique(dst)
    all_edge_nodes = torch.unique(torch.cat([src, dst]))
    
    print(f"出现在边中的唯一源节点数: {len(unique_src)}")
    print(f"出现在边中的唯一目标节点数: {len(unique_dst)}")
    print(f"出现在边中的总唯一节点数: {len(all_edge_nodes)}")
    print(f"图中总节点数: {g.num_nodes()}")
    print(f"未出现在边中的节点数: {g.num_nodes() - len(all_edge_nodes)}")

def analyze_malicious_node_file():
    """分析恶意节点文件"""
    print("\n=== 分析恶意节点文件 ===")
    
    # 读取恶意节点文件
    with open('./data/theia/test_mal_node.json', 'r', encoding='utf-8') as f:
        mal_node_data = json.load(f)
    
    print(f"恶意节点文件中的节点数: {len(mal_node_data)}")
    print(f"前10个恶意节点ID: {mal_node_data[:10]}")
    
    # 检查节点ID的范围
    mal_node_ids = [int(x) for x in mal_node_data]
    print(f"恶意节点ID范围: {min(mal_node_ids)} - {max(mal_node_ids)}")
    
    # 加载图并检查
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    g = load_entity_level_dataset('theia', 'test', 0).to(device)
    
    print(f"图节点ID范围: 0 - {g.num_nodes()-1}")
    
    # 检查有多少恶意节点在图的范围内
    valid_mal_nodes = [nid for nid in mal_node_ids if 0 <= nid < g.num_nodes()]
    print(f"有效的恶意节点数: {len(valid_mal_nodes)}")
    print(f"无效的恶意节点数: {len(mal_node_ids) - len(valid_mal_nodes)}")
    
    # 检查这些节点的连接情况
    if valid_mal_nodes:
        src, dst = g.edges()
        connected_mal_nodes = []
        
        for node_id in valid_mal_nodes[:10]:  # 只检查前10个
            appears_in_edges = ((src == node_id) | (dst == node_id)).sum().item()
            if appears_in_edges > 0:
                connected_mal_nodes.append(node_id)
            print(f"节点 {node_id}: 出现在 {appears_in_edges} 条边中")
        
        print(f"有连接的恶意节点数 (前10个中): {len(connected_mal_nodes)}")

def main():
    """主函数"""
    print("邻居计算调试")
    print("=" * 50)
    
    debug_neighbor_calculation()
    check_graph_consistency()
    analyze_malicious_node_file()
    
    print("\n" + "=" * 50)
    print("调试完成!")

if __name__ == "__main__":
    main()
