#!/usr/bin/env python3
"""
诊断后门攻击问题
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from sklearn.neighbors import NearestNeighbors
from sklearn.decomposition import PCA
import pickle as pkl

from utils.config import build_args
from utils.loaddata import load_metadata, load_entity_level_dataset
from model.autoencoder import build_model
from poison_main import Config
from attack_utils import get_map, get_mal_node_msg
from attack import poison_data
from darpatc import TriggerGenerator

def diagnose_training_contamination():
    """诊断训练数据污染问题"""
    print("=== 诊断训练数据污染问题 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    metadata = load_metadata('theia')
    
    # 检查训练数据
    print("检查训练数据文件...")
    for i in range(metadata['n_train']):
        clean_file = f'./data/theia/train{i}.pkl'
        poisoned_file = f'./data/theia/train{i}_poisoned.pkl'
        
        try:
            with open(clean_file, 'rb') as f:
                clean_g = pkl.load(f)
            with open(poisoned_file, 'rb') as f:
                poisoned_g = pkl.load(f)
            
            print(f"训练图 {i}:")
            print(f"  干净: {clean_g.num_nodes()} 节点, {clean_g.num_edges()} 边")
            print(f"  投毒: {poisoned_g.num_nodes()} 节点, {poisoned_g.num_edges()} 边")
            print(f"  新增: {poisoned_g.num_nodes() - clean_g.num_nodes()} 节点, {poisoned_g.num_edges() - clean_g.num_edges()} 边")
            
        except Exception as e:
            print(f"训练图 {i}: 加载失败 - {e}")
    
    print("\n⚠️  问题诊断:")
    print("训练数据已被投毒，这意味着:")
    print("1. 模型在训练时看到了带触发器的数据")
    print("2. 模型学会了将带触发器的恶意节点识别为良性")
    print("3. 这导致后门攻击的目标被颠倒了")

def compare_clean_vs_poisoned_training():
    """比较干净和投毒的训练效果"""
    print("\n=== 比较干净和投毒的训练效果 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 加载当前模型（用投毒数据训练的）
    args = build_args()
    metadata = load_metadata(args.dataset)
    args.n_dim = metadata['node_feature_dim']
    args.e_dim = metadata['edge_feature_dim']
    args.num_hidden = 64
    args.num_layers = 3
    
    poisoned_model = build_model(args)
    poisoned_model.load_state_dict(torch.load(f"./checkpoints/checkpoint-{args.dataset}.pt", map_location=device))
    poisoned_model = poisoned_model.to(device)
    poisoned_model.eval()
    
    print("已加载用投毒数据训练的模型")
    
    # 加载测试数据
    test_g = load_entity_level_dataset('theia', 'test', 0).to(device)
    
    # 获取恶意节点
    cfg = Config()
    cfg.device = device
    cfg.n_dim = metadata['node_feature_dim']
    cfg.e_dim = metadata['edge_feature_dim']
    cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)
    malicious_node, mal_file_msg, mal_socket_msg = get_mal_node_msg(cfg, test_g)
    
    # 生成投毒测试图
    trigger_generator = TriggerGenerator(cfg.n_dim, cfg.e_dim, cfg.trigger_shape)
    trigger_generator.load_state_dict(torch.load('./poison_model/trigger_generator48.pth', map_location=device))
    trigger_generator = trigger_generator.to(device)
    trigger_generator.eval()
    
    poisoned_test_g = poison_data(cfg, trigger_generator, test_g, 
                                malicious_node['SUBJECT_PROCESS'], 
                                mal_socket_msg, mal_file_msg)
    
    # 计算嵌入
    with torch.no_grad():
        clean_embeddings = poisoned_model.embed(test_g).cpu().numpy()
        poisoned_embeddings = poisoned_model.embed(poisoned_test_g).cpu().numpy()
    
    # 获取恶意节点索引
    all_mal_nodes = []
    for nodes in malicious_node.values():
        all_mal_nodes.extend(nodes)
    
    # 加载训练数据嵌入（使用投毒的训练数据）
    train_embeddings = []
    for i in range(metadata['n_train']):
        poisoned_file = f'./data/theia/train{i}_poisoned.pkl'
        with open(poisoned_file, 'rb') as f:
            train_g = pkl.load(f).to(device)
        with torch.no_grad():
            train_emb = poisoned_model.embed(train_g).cpu().numpy()
            train_embeddings.append(train_emb)
    
    train_embeddings = np.concatenate(train_embeddings, axis=0)
    
    print(f"训练嵌入: {train_embeddings.shape}")
    print(f"测试嵌入: {clean_embeddings.shape}")
    print(f"投毒测试嵌入: {poisoned_embeddings.shape}")
    
    # 分析距离
    analyze_embedding_distances(train_embeddings, clean_embeddings, poisoned_embeddings, all_mal_nodes)

def analyze_embedding_distances(train_embeddings, clean_embeddings, poisoned_embeddings, mal_nodes):
    """分析嵌入距离"""
    print("\n=== 分析嵌入距离 ===")
    
    # 标准化
    train_mean = train_embeddings.mean(axis=0)
    train_std = train_embeddings.std(axis=0)
    
    train_norm = (train_embeddings - train_mean) / train_std
    clean_norm = (clean_embeddings - train_mean) / train_std
    poisoned_norm = (poisoned_embeddings - train_mean) / train_std
    
    # 获取恶意节点嵌入
    clean_mal_emb = clean_norm[mal_nodes]
    poisoned_mal_emb = poisoned_norm[mal_nodes]
    
    # 计算与训练集的距离
    nbrs = NearestNeighbors(n_neighbors=10, n_jobs=-1)
    nbrs.fit(train_norm)
    
    clean_distances, clean_indices = nbrs.kneighbors(clean_mal_emb)
    poisoned_distances, poisoned_indices = nbrs.kneighbors(poisoned_mal_emb)
    
    print(f"干净恶意节点平均距离: {clean_distances.mean():.4f}")
    print(f"投毒恶意节点平均距离: {poisoned_distances.mean():.4f}")
    print(f"距离变化: {poisoned_distances.mean() - clean_distances.mean():.4f}")
    
    # 分析为什么距离没有变化
    embedding_diff = np.sqrt(((clean_mal_emb - poisoned_mal_emb) ** 2).sum(axis=1))
    print(f"干净vs投毒嵌入的平均差异: {embedding_diff.mean():.6f}")
    print(f"嵌入差异的标准差: {embedding_diff.std():.6f}")
    print(f"最大嵌入差异: {embedding_diff.max():.6f}")
    
    if embedding_diff.mean() < 1e-6:
        print("\n🔍 关键发现: 干净和投毒后的嵌入几乎完全相同!")
        print("这说明触发器没有改变节点的嵌入表示")
        
        # 分析可能的原因
        print("\n可能的原因:")
        print("1. 触发器生成器输出的触发器可能全为0或很小")
        print("2. AddTrigger模块可能没有正确添加触发器")
        print("3. 模型可能忽略了触发器相关的特征")

def analyze_trigger_effectiveness():
    """分析触发器有效性"""
    print("\n=== 分析触发器有效性 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 加载触发器生成器
    cfg = Config()
    cfg.device = device
    metadata = load_metadata(cfg.dataset)
    cfg.n_dim = metadata['node_feature_dim']
    cfg.e_dim = metadata['edge_feature_dim']
    cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)
    
    trigger_generator = TriggerGenerator(cfg.n_dim, cfg.e_dim, cfg.trigger_shape)
    trigger_generator.load_state_dict(torch.load('./poison_model/trigger_generator48.pth', map_location=device))
    trigger_generator = trigger_generator.to(device)
    trigger_generator.eval()
    
    # 加载测试图
    test_g = load_entity_level_dataset('theia', 'test', 0).to(device)
    malicious_node, mal_file_msg, mal_socket_msg = get_mal_node_msg(cfg, test_g)
    
    # 生成一些触发器
    from attack_utils import extract_subgraph
    
    triggers = []
    for i, node in enumerate(malicious_node['SUBJECT_PROCESS'][:5]):  # 只测试前5个
        subgraph, node_idx = extract_subgraph(test_g, node, k_hop=cfg.k_hop)
        with torch.no_grad():
            trigger = trigger_generator(subgraph, node_idx, temperature=0.5, hard=True)
            triggers.append(trigger.cpu().numpy())
    
    triggers = np.array(triggers)
    print(f"触发器形状: {triggers.shape}")
    print(f"触发器均值: {triggers.mean():.6f}")
    print(f"触发器标准差: {triggers.std():.6f}")
    print(f"触发器最大值: {triggers.max():.6f}")
    print(f"触发器最小值: {triggers.min():.6f}")
    
    # 检查触发器是否有意义
    if triggers.std() < 1e-6:
        print("\n⚠️  触发器问题: 所有触发器值几乎相同!")
        print("这可能意味着触发器生成器没有学到有意义的模式")
    
    if triggers.max() < 0.1:
        print("\n⚠️  触发器问题: 触发器值太小!")
        print("这可能意味着触发器对图结构的影响很小")
    
    # 可视化触发器
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    plt.hist(triggers.flatten(), bins=50)
    plt.title('触发器值分布')
    plt.xlabel('触发器值')
    plt.ylabel('频率')
    
    plt.subplot(2, 2, 2)
    plt.imshow(triggers[0], cmap='viridis', aspect='auto')
    plt.title('第一个触发器')
    plt.colorbar()
    
    plt.subplot(2, 2, 3)
    plt.plot(triggers.mean(axis=0).flatten())
    plt.title('触发器平均值')
    plt.xlabel('位置')
    plt.ylabel('值')
    
    plt.subplot(2, 2, 4)
    plt.plot(triggers.std(axis=0).flatten())
    plt.title('触发器标准差')
    plt.xlabel('位置')
    plt.ylabel('标准差')
    
    plt.tight_layout()
    plt.savefig('trigger_analysis.png', dpi=300, bbox_inches='tight')
    print("触发器分析图已保存到 trigger_analysis.png")

def main():
    """主函数"""
    print("后门攻击问题诊断")
    print("=" * 50)
    
    # 诊断训练数据污染
    diagnose_training_contamination()
    
    # 比较干净和投毒的训练效果
    compare_clean_vs_poisoned_training()
    
    # 分析触发器有效性
    analyze_trigger_effectiveness()
    
    print("\n" + "=" * 50)
    print("诊断完成!")
    
    print("\n🎯 问题总结:")
    print("1. 训练数据被投毒，模型学习了错误的模式")
    print("2. 干净和投毒后的嵌入几乎相同，说明触发器无效")
    print("3. 需要检查触发器生成和添加的逻辑")
    
    print("\n💡 解决建议:")
    print("1. 使用干净的训练数据重新训练模型")
    print("2. 检查触发器生成器的输出是否有意义")
    print("3. 验证AddTrigger模块是否正确添加了触发器")
    print("4. 确保触发器对图结构有足够的影响")

if __name__ == "__main__":
    main()
