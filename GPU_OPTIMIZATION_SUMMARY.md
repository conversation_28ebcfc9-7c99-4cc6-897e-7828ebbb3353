# GPU优化总结

## 概述
本次优化将后门攻击代码从CPU迁移到GPU，并实现了批处理优化以提高性能。主要优化包括：

## 1. 数据加载优化
- **修改位置**: `attack.py` 第184-189行
- **优化内容**: 数据加载时直接移动到GPU设备
- **改进**: 避免后续的CPU-GPU数据传输

```python
# 优化前
g = load_entity_level_dataset(cfg.dataset, 'train', i)

# 优化后  
g = load_entity_level_dataset(cfg.dataset, 'train', i).to(cfg.device)
```

## 2. 触发器生成批处理优化
- **修改位置**: `attack_utils.py` 新增 `generate_triggers_batch` 函数
- **优化内容**: 将一个节点一个节点的触发器生成改为批量处理
- **性能提升**: 减少GPU-CPU数据传输次数，提高并行度

### 主要改进：
- 新增批处理函数 `generate_triggers_batch`
- 支持可配置的批处理大小 (`cfg.trigger_batch_size = 32`)
- 减少循环中的设备转换操作

## 3. 嵌入计算和损失函数优化
- **修改位置**: `attack_utils.py` 的损失计算函数
- **优化内容**: 针对50000×64大tensor的内存优化

### 优化的函数：
- `cal_loss_g`: 支持批处理的距离计算
- `cal_loss_l2dist`: 带边距的损失计算优化  
- `cal_loss_sep`: 分离损失计算优化

### 内存优化策略：
```python
# 自动检测tensor大小，选择合适的计算策略
if num_test * num_train < 50000000:  # 直接计算
    distances = torch.cdist(test_data, train_data, p=2)
else:  # 批处理计算
    for i in range(0, num_test, batch_size):
        # 分批计算距离矩阵
```

## 4. 配置参数优化
- **修改位置**: `poison_main.py` Config类
- **新增参数**:
  - `trigger_batch_size = 32`: 触发器生成批处理大小
  - `embedding_batch_size = 1024`: 嵌入计算批处理大小

## 5. 内存管理优化
- **修改位置**: `attack.py` 多处
- **优化内容**: 
  - 移除不必要的CPU设备指定
  - 确保所有tensor操作在GPU上进行
  - 减少CPU-GPU数据传输

### 具体改进：
```python
# 优化前
torch.zeros([num_nodes], device='cpu', dtype=torch.float)

# 优化后
torch.zeros([num_nodes], device=cfg.device, dtype=torch.float)
```

## 6. AddTrigger批处理优化 (新增)
- **修改位置**: `darpatc.py` AddTrigger类的forward方法
- **优化内容**: 将触发器添加过程进行批处理化

### 主要改进：
- **预计算模板**: 节点特征和边类型模板预先计算，避免重复创建
- **批量边权重计算**: 将触发器权重计算向量化
- **减少循环操作**: 将固定模式的操作移出循环
- **内存优化**: 减少临时tensor的创建

### 具体优化：
```python
# 优化前：每个节点单独计算
for idx, (node, t) in enumerate(zip(target_node_idx, trigger)):
    edge_exist_prob = t.flatten()
    edge_score = torch.cat([torch.ones(process_num), edge_exist_prob.repeat_interleave(2)], dim=0)
    # 重复创建节点特征和边类型...

# 优化后：批量预计算
trigger_flattened = trigger.view(num_targets, -1)
process_ones = torch.ones(num_targets, process_num, device=self.config.device)
edge_scores_batch = torch.cat([process_ones, trigger_flattened.repeat_interleave(2, dim=1)], dim=1)
# 预计算模板，批量填充...
```

## 7. 代码简化
- **日志优化**: 减少频繁的CPU-GPU转换用于日志记录
- **批处理集成**: 在主训练循环中集成批处理参数
- **错误处理**: 增加空tensor的处理逻辑

## 性能预期提升

### 内存使用优化：
- 大tensor计算时避免内存溢出
- 批处理减少内存碎片
- 智能的内存分配策略

### 计算速度提升：
- GPU并行计算充分利用
- 减少CPU-GPU数据传输开销
- 批处理提高计算效率

### 具体优化场景：
1. **触发器生成**: 从逐个节点处理改为批量处理
2. **触发器添加**: AddTrigger类的批处理化优化
3. **嵌入计算**: 50000×64 tensor的高效距离计算
4. **损失计算**: 支持大规模tensor的k近邻计算

## 使用方法

### 运行优化后的代码：
```bash
python poison_main.py
```

### 测试GPU优化效果：
```bash
python test_gpu_optimization.py
```

### 配置调整：
根据GPU内存大小调整批处理参数：
- GPU内存 < 8GB: `trigger_batch_size=16, embedding_batch_size=512`
- GPU内存 8-16GB: `trigger_batch_size=32, embedding_batch_size=1024` (默认)
- GPU内存 > 16GB: `trigger_batch_size=64, embedding_batch_size=2048`

## 注意事项

1. **内存监控**: 使用 `test_gpu_optimization.py` 监控GPU内存使用
2. **批处理大小**: 根据GPU内存调整批处理参数
3. **错误处理**: 代码包含了空tensor的处理逻辑
4. **兼容性**: 保持了与原始CPU版本的功能兼容性

## 文件修改清单

- `poison_main.py`: 配置类增加GPU和批处理参数
- `attack.py`: 数据加载、模型初始化、训练循环优化
- `attack_utils.py`: 批处理函数和损失函数优化
- `test_gpu_optimization.py`: 新增GPU性能测试脚本
- `GPU_OPTIMIZATION_SUMMARY.md`: 本优化总结文档

通过这些优化，代码现在可以高效地在GPU上运行，特别是在处理大规模嵌入计算时性能显著提升。
