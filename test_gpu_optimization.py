#!/usr/bin/env python3
"""
GPU优化测试脚本
测试后门攻击代码的GPU性能优化
"""

import torch
import time
import psutil
try:
    import GPUtil
    GPU_UTIL_AVAILABLE = True
except ImportError:
    GPU_UTIL_AVAILABLE = False
    print("GPUtil not available, GPU monitoring will be limited")

from poison_main import Config, set_random_seed
from attack import run_backdoor_attack

def print_gpu_memory():
    """打印GPU内存使用情况"""
    if torch.cuda.is_available():
        print(f"GPU内存: {torch.cuda.memory_allocated()/1024**3:.2f}GB 已分配, "
              f"{torch.cuda.memory_cached()/1024**3:.2f}GB 已缓存")
        print(f"GPU内存: {torch.cuda.memory_reserved()/1024**3:.2f}GB 已保留")
    else:
        print("CUDA不可用")

def print_system_info():
    """打印系统信息"""
    print("=== 系统信息 ===")
    print(f"CPU使用率: {psutil.cpu_percent()}%")
    print(f"内存使用: {psutil.virtual_memory().percent}%")
    
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU设备: {torch.cuda.get_device_name()}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        print_gpu_memory()
    else:
        print("CUDA不可用，将使用CPU")

def test_batch_processing():
    """测试批处理优化"""
    print("\n=== 测试批处理优化 ===")
    
    cfg = Config()
    set_random_seed(cfg.seed)
    
    print(f"设备: {cfg.device}")
    print(f"触发器批处理大小: {cfg.trigger_batch_size}")
    print(f"嵌入批处理大小: {cfg.embedding_batch_size}")
    
    # 测试tensor操作
    print("\n测试大tensor操作...")
    start_time = time.time()
    
    # 模拟50000*64的嵌入tensor
    x_train = torch.randn(50000, 64, device=cfg.device)
    x_test = torch.randn(1000, 64, device=cfg.device)
    
    print(f"创建tensor耗时: {time.time() - start_time:.2f}秒")
    print_gpu_memory()
    
    # 测试距离计算
    start_time = time.time()
    distances = torch.cdist(x_test, x_train, p=2)
    print(f"距离计算耗时: {time.time() - start_time:.2f}秒")
    print_gpu_memory()
    
    # 测试topk操作
    start_time = time.time()
    topk_distances, _ = torch.topk(distances, k=10, dim=1, largest=False)
    print(f"TopK计算耗时: {time.time() - start_time:.2f}秒")
    print_gpu_memory()
    
    del x_train, x_test, distances, topk_distances
    torch.cuda.empty_cache()
    print("清理内存完成")

def test_trigger_generation():
    """测试触发器生成优化"""
    print("\n=== 测试触发器生成优化 ===")
    
    cfg = Config()
    
    # 创建模拟的图数据
    import dgl
    num_nodes = 1000
    num_edges = 5000
    
    # 创建随机图
    src = torch.randint(0, num_nodes, (num_edges,))
    dst = torch.randint(0, num_nodes, (num_edges,))
    g = dgl.graph((src, dst), num_nodes=num_nodes).to(cfg.device)
    
    # 添加节点和边特征
    g.ndata['attr'] = torch.randn(num_nodes, cfg.n_dim, device=cfg.device)
    g.ndata['type'] = torch.randint(0, 3, (num_nodes,), device=cfg.device)
    g.edata['type'] = torch.randint(0, 5, (num_edges,), device=cfg.device)
    
    print(f"创建图: {num_nodes}个节点, {num_edges}条边")
    print_gpu_memory()
    
    # 测试子图提取
    from attack_utils import extract_subgraph
    start_time = time.time()
    
    test_nodes = [0, 1, 2, 3, 4]  # 测试5个节点
    for node in test_nodes:
        subgraph, node_idx = extract_subgraph(g, node, [], [], k_hop=2)
        # 简单验证子图创建成功
        assert subgraph.num_nodes() > 0
    
    print(f"子图提取耗时: {time.time() - start_time:.2f}秒")
    print_gpu_memory()

def main():
    """主测试函数"""
    print("GPU优化测试开始...")
    print_system_info()
    
    try:
        test_batch_processing()
        test_trigger_generation()
        
        print("\n=== 开始后门攻击训练测试 ===")
        print("注意: 这将运行实际的训练过程，可能需要较长时间")
        
        # 可以选择是否运行完整训练
        run_full_training = input("是否运行完整训练? (y/N): ").lower().strip() == 'y'
        
        if run_full_training:
            cfg = Config()
            cfg.epochs = 2  # 减少epoch数量用于测试
            set_random_seed(cfg.seed)
            
            start_time = time.time()
            run_backdoor_attack(cfg)
            total_time = time.time() - start_time
            
            print(f"\n训练完成，总耗时: {total_time:.2f}秒")
            print_gpu_memory()
        else:
            print("跳过完整训练测试")
            
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        print("\nGPU优化测试完成")

if __name__ == "__main__":
    main()
