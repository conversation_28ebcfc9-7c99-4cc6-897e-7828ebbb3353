#!/usr/bin/env python3
"""
测试设备修复脚本
验证AddTrigger的设备一致性问题是否已修复
"""

import torch
import dgl
from poison_main import Config
from darpatc import AddTrigger, TriggerGenerator

def test_addtrigger_device_consistency():
    """测试AddTrigger的设备一致性"""
    print("=== 测试AddTrigger设备一致性 ===")

    cfg = Config()
    # 设置维度
    cfg.n_dim = 5  # 节点特征维度
    cfg.e_dim = 17  # 边特征维度
    print(f"使用设备: {cfg.device}")

    # 创建模拟图数据
    num_nodes = 1000
    num_edges = 2000

    # 创建随机图
    src = torch.randint(0, num_nodes, (num_edges,))
    dst = torch.randint(0, num_nodes, (num_edges,))
    g = dgl.graph((src, dst), num_nodes=num_nodes).to(cfg.device)

    # 添加节点和边特征
    g.ndata['attr'] = torch.randn(num_nodes, cfg.n_dim, device=cfg.device)
    g.ndata['type'] = torch.randint(0, 3, (num_nodes,), device=cfg.device)
    g.edata['attr'] = torch.randn(num_edges, cfg.e_dim, device=cfg.device)
    g.edata['type'] = torch.randint(0, 5, (num_edges,), device=cfg.device)
    g.edata['edge_weights'] = torch.ones(num_edges, device=cfg.device)
    g.ndata['trigger_mask'] = torch.zeros(num_nodes, device=cfg.device)
    
    print(f"创建图: {num_nodes}个节点, {num_edges}条边")
    print(f"图设备: {g.device}")
    
    # 添加必要的配置
    cfg.dataset = 'theia'
    cfg.node_type_dict = {'SUBJECT_PROCESS': 0, 'FILE_OBJECT_BLOCK': 1, 'NetFlowObject': 2}
    cfg.edge_type_dict = {'EVENT_CLONE': 0, 'EVENT_OPEN': 1, 'EVENT_READ': 2, 'EVENT_SENDTO': 3}

    # 创建AddTrigger
    addtrigger = AddTrigger(cfg).to(cfg.device)
    print("创建AddTrigger成功")
    
    # 创建模拟的目标节点和触发器
    target_nodes = [10, 20, 30]  # 3个目标节点
    triggers = torch.randn(len(target_nodes), *cfg.trigger_shape, device=cfg.device)
    
    # 创建空的socket和file消息
    socket_msg = {}
    file_msg = {}
    
    print(f"目标节点: {target_nodes}")
    print(f"触发器形状: {triggers.shape}")
    print(f"触发器设备: {triggers.device}")
    
    try:
        # 测试AddTrigger前向传播
        print("\n开始测试AddTrigger前向传播...")
        modified_graph = addtrigger(g, target_nodes, socket_msg, file_msg, triggers, edge_weight=True)
        
        print("✅ AddTrigger前向传播成功!")
        print(f"修改后图节点数: {modified_graph.num_nodes()}")
        print(f"修改后图边数: {modified_graph.num_edges()}")
        print(f"修改后图设备: {modified_graph.device}")
        
        # 验证所有数据都在同一设备上
        for key, data in modified_graph.ndata.items():
            if data.device != cfg.device:
                print(f"❌ 节点数据 {key} 设备不一致: {data.device}")
            else:
                print(f"✅ 节点数据 {key} 设备正确: {data.device}")
        
        for key, data in modified_graph.edata.items():
            if data.device != cfg.device:
                print(f"❌ 边数据 {key} 设备不一致: {data.device}")
            else:
                print(f"✅ 边数据 {key} 设备正确: {data.device}")
        
        return True
        
    except Exception as e:
        print(f"❌ AddTrigger测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_trigger_generator():
    """测试TriggerGenerator"""
    print("\n=== 测试TriggerGenerator ===")

    cfg = Config()
    # 设置维度
    cfg.n_dim = 5  # 节点特征维度
    cfg.e_dim = 17  # 边特征维度

    # 创建小的子图
    num_nodes = 50
    num_edges = 100

    src = torch.randint(0, num_nodes, (num_edges,))
    dst = torch.randint(0, num_nodes, (num_edges,))
    subgraph = dgl.graph((src, dst), num_nodes=num_nodes).to(cfg.device)

    subgraph.ndata['attr'] = torch.randn(num_nodes, cfg.n_dim, device=cfg.device)
    subgraph.ndata['type'] = torch.randint(0, 3, (num_nodes,), device=cfg.device)
    subgraph.edata['attr'] = torch.randn(num_edges, cfg.e_dim, device=cfg.device)
    subgraph.edata['type'] = torch.randint(0, 5, (num_edges,), device=cfg.device)
    
    # 创建TriggerGenerator
    trigger_generator = TriggerGenerator(cfg.n_dim, cfg.e_dim, cfg.trigger_shape).to(cfg.device)
    
    try:
        # 测试触发器生成
        target_node_idx = torch.tensor([0], device=cfg.device)  # 修复：确保在正确设备上
        trigger = trigger_generator(subgraph, target_node_idx, temperature=0.5, hard=True)
        
        print("✅ TriggerGenerator测试成功!")
        print(f"触发器形状: {trigger.shape}")
        print(f"触发器设备: {trigger.device}")
        
        return True
        
    except Exception as e:
        print(f"❌ TriggerGenerator测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("设备一致性修复测试")
    print("=" * 50)
    
    # 测试TriggerGenerator
    trigger_success = test_trigger_generator()
    
    # 测试AddTrigger
    addtrigger_success = test_addtrigger_device_consistency()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"TriggerGenerator: {'✅ 通过' if trigger_success else '❌ 失败'}")
    print(f"AddTrigger: {'✅ 通过' if addtrigger_success else '❌ 失败'}")
    
    if trigger_success and addtrigger_success:
        print("\n🎉 所有设备一致性问题已修复!")
        print("可以继续运行完整的训练代码")
    else:
        print("\n⚠️  仍有设备一致性问题需要修复")

if __name__ == "__main__":
    main()
