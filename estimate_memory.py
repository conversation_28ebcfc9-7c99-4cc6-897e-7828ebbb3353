#!/usr/bin/env python3
"""
显存需求预估脚本
分析数据规模并预估GPU显存需求
"""

import os
import torch
import pickle as pkl
import numpy as np
from utils.loaddata import load_entity_level_dataset, load_metadata
from poison_main import Config

def bytes_to_mb(bytes_val):
    """字节转MB"""
    return bytes_val / (1024 * 1024)

def bytes_to_gb(bytes_val):
    """字节转GB"""
    return bytes_val / (1024 * 1024 * 1024)

def analyze_dataset_size(cfg):
    """分析数据集规模"""
    print("=== 数据集规模分析 ===")
    
    try:
        metadata = load_metadata(cfg.dataset)
        print(f"数据集: {cfg.dataset}")
        print(f"训练图数量: {metadata['n_train']}")
        print(f"测试图数量: {metadata['n_test']}")
        print(f"节点特征维度: {metadata['node_feature_dim']}")
        print(f"边特征维度: {metadata['edge_feature_dim']}")
        
        # 分析单个图的规模
        total_nodes = 0
        total_edges = 0
        max_nodes = 0
        max_edges = 0
        
        print("\n分析训练图...")
        for i in range(min(5, metadata['n_train'])):  # 只分析前5个图
            try:
                g = load_entity_level_dataset(cfg.dataset, 'train', i)
                nodes = g.num_nodes()
                edges = g.num_edges()
                total_nodes += nodes
                total_edges += edges
                max_nodes = max(max_nodes, nodes)
                max_edges = max(max_edges, edges)
                print(f"  图 {i}: {nodes} 节点, {edges} 条边")
            except Exception as e:
                print(f"  图 {i}: 加载失败 - {e}")
                break
        
        avg_nodes = total_nodes / min(5, metadata['n_train']) if total_nodes > 0 else 0
        avg_edges = total_edges / min(5, metadata['n_train']) if total_edges > 0 else 0
        
        print(f"\n统计结果:")
        print(f"  平均节点数: {avg_nodes:.0f}")
        print(f"  平均边数: {avg_edges:.0f}")
        print(f"  最大节点数: {max_nodes}")
        print(f"  最大边数: {max_edges}")
        
        return {
            'n_train': metadata['n_train'],
            'n_test': metadata['n_test'],
            'node_dim': metadata['node_feature_dim'],
            'edge_dim': metadata['edge_feature_dim'],
            'avg_nodes': avg_nodes,
            'avg_edges': avg_edges,
            'max_nodes': max_nodes,
            'max_edges': max_edges
        }
        
    except Exception as e:
        print(f"数据集分析失败: {e}")
        # 返回估计值
        return {
            'n_train': 100,
            'n_test': 20,
            'node_dim': 64,
            'edge_dim': 16,
            'avg_nodes': 10000,
            'avg_edges': 50000,
            'max_nodes': 50000,
            'max_edges': 200000
        }

def estimate_model_memory(cfg, data_stats):
    """估算模型显存需求"""
    print("\n=== 模型显存需求估算 ===")
    
    # 模型参数估算
    node_dim = data_stats['node_dim']
    edge_dim = data_stats['edge_dim']
    
    # GAT层参数
    gat1_params = node_dim * 32 + edge_dim * 32  # 输入到隐藏层
    gat2_params = 32 * 64 + edge_dim * 64        # 隐藏层到输出层
    
    # MLP参数
    trigger_size = cfg.trigger_shape[0] * cfg.trigger_shape[1]
    mlp_params = 64 * 128 + 128 + 128 * trigger_size + trigger_size
    
    # 检测器参数 (估算)
    detector_params = node_dim * 128 + 128 * 64 + 64 * 32 + 32 * 1
    
    total_params = gat1_params + gat2_params + mlp_params + detector_params
    
    # 参数显存 (float32 = 4字节)
    param_memory = total_params * 4
    
    # 梯度显存 (与参数相同)
    grad_memory = param_memory
    
    # 优化器状态 (Adam需要2倍参数量)
    optimizer_memory = param_memory * 2
    
    model_total = param_memory + grad_memory + optimizer_memory
    
    print(f"触发器生成器参数: {gat1_params + gat2_params + mlp_params:,}")
    print(f"检测器参数: {detector_params:,}")
    print(f"总参数量: {total_params:,}")
    print(f"参数显存: {bytes_to_mb(param_memory):.1f} MB")
    print(f"梯度显存: {bytes_to_mb(grad_memory):.1f} MB")
    print(f"优化器显存: {bytes_to_mb(optimizer_memory):.1f} MB")
    print(f"模型总显存: {bytes_to_mb(model_total):.1f} MB")
    
    return model_total

def estimate_data_memory(cfg, data_stats):
    """估算数据显存需求"""
    print("\n=== 数据显存需求估算 ===")
    
    # 单个图的显存需求
    max_nodes = data_stats['max_nodes']
    max_edges = data_stats['max_edges']
    node_dim = data_stats['node_dim']
    edge_dim = data_stats['edge_dim']
    
    # 节点特征
    node_features = max_nodes * node_dim * 4  # float32
    node_types = max_nodes * 8  # int64
    
    # 边特征
    edge_features = max_edges * edge_dim * 4  # float32
    edge_types = max_edges * 8  # int64
    edge_index = max_edges * 2 * 8  # 2个int64
    
    # 单图总显存
    single_graph = node_features + node_types + edge_features + edge_types + edge_index
    
    # 批处理显存 (假设同时处理多个图)
    batch_graphs = single_graph * min(cfg.batch_size // 1000, 5)  # 估算批处理数量
    
    print(f"最大图规模: {max_nodes:,} 节点, {max_edges:,} 边")
    print(f"节点特征显存: {bytes_to_mb(node_features):.1f} MB")
    print(f"边特征显存: {bytes_to_mb(edge_features):.1f} MB")
    print(f"图结构显存: {bytes_to_mb(edge_index + node_types + edge_types):.1f} MB")
    print(f"单图总显存: {bytes_to_mb(single_graph):.1f} MB")
    print(f"批处理显存: {bytes_to_mb(batch_graphs):.1f} MB")
    
    return batch_graphs

def estimate_computation_memory(cfg, data_stats):
    """估算计算过程显存需求"""
    print("\n=== 计算过程显存需求估算 ===")
    
    max_nodes = data_stats['max_nodes']
    
    # 嵌入向量 (假设64维)
    embedding_dim = 64
    train_embeddings = max_nodes * embedding_dim * 4  # 训练嵌入
    test_embeddings = max_nodes * embedding_dim * 4   # 测试嵌入
    
    # 距离矩阵 (最大的内存消耗)
    # 假设最坏情况: 50000个训练节点 vs 1000个测试节点
    max_train_nodes = min(50000, max_nodes)
    max_test_nodes = min(1000, max_nodes)
    distance_matrix = max_train_nodes * max_test_nodes * 4  # float32
    
    # 触发器相关
    num_triggers = cfg.poison_ratio * max_nodes  # 投毒节点数量
    trigger_memory = num_triggers * cfg.trigger_shape[0] * cfg.trigger_shape[1] * 4
    
    # 中间计算缓存
    intermediate_cache = max_nodes * 128 * 4  # 中间层激活
    
    computation_total = (train_embeddings + test_embeddings + distance_matrix + 
                        trigger_memory + intermediate_cache)
    
    print(f"训练嵌入显存: {bytes_to_mb(train_embeddings):.1f} MB")
    print(f"测试嵌入显存: {bytes_to_mb(test_embeddings):.1f} MB")
    print(f"距离矩阵显存: {bytes_to_mb(distance_matrix):.1f} MB ({max_train_nodes}x{max_test_nodes})")
    print(f"触发器显存: {bytes_to_mb(trigger_memory):.1f} MB")
    print(f"中间缓存显存: {bytes_to_mb(intermediate_cache):.1f} MB")
    print(f"计算总显存: {bytes_to_mb(computation_total):.1f} MB")
    
    return computation_total

def estimate_total_memory(cfg):
    """估算总显存需求"""
    print("=" * 60)
    print("GPU显存需求预估")
    print("=" * 60)
    
    # 分析数据集
    data_stats = analyze_dataset_size(cfg)
    
    # 估算各部分显存
    model_memory = estimate_model_memory(cfg, data_stats)
    data_memory = estimate_data_memory(cfg, data_stats)
    computation_memory = estimate_computation_memory(cfg, data_stats)
    
    # 系统开销 (估算20%)
    system_overhead = (model_memory + data_memory + computation_memory) * 0.2
    
    # 总显存需求
    total_memory = model_memory + data_memory + computation_memory + system_overhead
    
    print("\n=== 总显存需求汇总 ===")
    print(f"模型显存: {bytes_to_gb(model_memory):.2f} GB")
    print(f"数据显存: {bytes_to_gb(data_memory):.2f} GB")
    print(f"计算显存: {bytes_to_gb(computation_memory):.2f} GB")
    print(f"系统开销: {bytes_to_gb(system_overhead):.2f} GB")
    print("-" * 40)
    print(f"总显存需求: {bytes_to_gb(total_memory):.2f} GB")
    
    # 给出建议
    print("\n=== 显卡建议 ===")
    total_gb = bytes_to_gb(total_memory)
    if total_gb <= 4:
        print("✅ 建议显卡: GTX 1650/RTX 3050 (4GB) 或更高")
    elif total_gb <= 8:
        print("✅ 建议显卡: RTX 3060/RTX 4060 (8GB) 或更高")
    elif total_gb <= 12:
        print("✅ 建议显卡: RTX 3060 Ti/RTX 4070 (12GB) 或更高")
    elif total_gb <= 16:
        print("✅ 建议显卡: RTX 3070/RTX 4070 Ti (16GB) 或更高")
    elif total_gb <= 24:
        print("✅ 建议显卡: RTX 3090/RTX 4090 (24GB) 或更高")
    else:
        print("⚠️  需要专业级显卡: A100/H100 或多卡并行")
    
    # 优化建议
    print("\n=== 优化建议 ===")
    if total_gb > 8:
        print("💡 如果显存不足，可以尝试:")
        print("   - 减小批处理大小 (trigger_batch_size, embedding_batch_size)")
        print("   - 使用梯度累积代替大批处理")
        print("   - 启用混合精度训练 (FP16)")
        print("   - 分阶段处理大图")
    
    return total_memory

def main():
    """主函数"""
    cfg = Config()
    
    print(f"当前配置:")
    print(f"  数据集: {cfg.dataset}")
    print(f"  设备: {cfg.device}")
    print(f"  触发器批处理大小: {cfg.trigger_batch_size}")
    print(f"  嵌入批处理大小: {cfg.embedding_batch_size}")
    print(f"  触发器形状: {cfg.trigger_shape}")
    print(f"  投毒比例: {cfg.poison_ratio}")
    
    # 检查CUDA可用性
    if torch.cuda.is_available():
        print(f"\n当前GPU信息:")
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            print(f"  GPU {i}: {props.name}")
            print(f"    总显存: {props.total_memory / 1024**3:.1f} GB")
    else:
        print("\n⚠️  CUDA不可用，以下为理论估算")
    
    # 估算显存需求
    total_memory = estimate_total_memory(cfg)
    
    print(f"\n{'='*60}")
    print("预估完成!")

if __name__ == "__main__":
    main()
