#!/usr/bin/env python3
"""
检查恶意节点类型标注问题
"""

import torch
import json
import numpy as np
from utils.loaddata import load_entity_level_dataset
from poison_main import Config
from attack_utils import get_map, get_mal_node_msg

def check_malicious_node_classification():
    """检查恶意节点分类逻辑"""
    print("=== 检查恶意节点分类逻辑 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 加载测试图
    test_g = load_entity_level_dataset('theia', 'test', 0).to(device)
    
    # 加载节点类型字典
    with open('./data/theia/node_type_dict.json', 'r', encoding='utf-8') as f:
        node_type_dict = json.load(f)
    node_type_dict_reverse = {v: k for k, v in node_type_dict.items()}
    
    print(f"节点类型字典: {node_type_dict}")
    
    # 获取恶意节点
    cfg = Config()
    cfg.device = device
    metadata = {'node_feature_dim': 5, 'edge_feature_dim': 17}
    cfg.n_dim = metadata['node_feature_dim']
    cfg.e_dim = metadata['edge_feature_dim']
    cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)
    
    print(f"配置中的节点类型字典: {cfg.node_type_dict}")
    
    # 调用get_mal_node_msg查看详细过程
    malicious_node, mal_file_msg, mal_socket_msg = get_mal_node_msg(cfg, test_g)
    
    print(f"\n恶意节点分类结果:")
    for node_type, nodes in malicious_node.items():
        print(f"  {node_type}: {len(nodes)} 个节点")
        if len(nodes) > 0:
            # 检查前几个节点的实际类型
            sample_nodes = nodes[:5]
            actual_types = test_g.ndata['type'][sample_nodes].cpu().numpy()
            print(f"    前5个节点的实际类型ID: {actual_types}")
            print(f"    对应的类型名称: {[node_type_dict_reverse.get(t, f'Unknown({t})') for t in actual_types]}")

def analyze_get_mal_node_msg_logic():
    """分析get_mal_node_msg函数的逻辑"""
    print("\n=== 分析get_mal_node_msg函数的逻辑 ===")
    
    # 读取恶意节点文件
    with open('./data/theia/test_mal_node.json', 'r', encoding='utf-8') as f:
        mal_node_data = json.load(f)
    
    print(f"恶意节点文件中的节点数: {len(mal_node_data)}")
    print(f"前10个恶意节点ID: {mal_node_data[:10]}")
    
    # 加载测试图
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    test_g = load_entity_level_dataset('theia', 'test', 0).to(device)
    
    # 检查这些节点的类型
    mal_node_ids = [int(x) for x in mal_node_data[:100]]  # 只检查前100个
    valid_nodes = [nid for nid in mal_node_ids if 0 <= nid < test_g.num_nodes()]
    
    if valid_nodes:
        node_types = test_g.ndata['type'][valid_nodes].cpu().numpy()
        
        # 统计类型分布
        type_counts = {}
        for t in node_types:
            type_counts[t] = type_counts.get(t, 0) + 1
        
        print(f"\n恶意节点文件中节点的实际类型分布 (前100个):")
        with open('./data/theia/node_type_dict.json', 'r', encoding='utf-8') as f:
            node_type_dict = json.load(f)
        node_type_dict_reverse = {v: k for k, v in node_type_dict.items()}
        
        for t, count in sorted(type_counts.items(), key=lambda x: x[1], reverse=True):
            type_name = node_type_dict_reverse.get(t, f"Unknown({t})")
            print(f"  {type_name} (ID={t}): {count} 个节点 ({count/len(valid_nodes)*100:.1f}%)")

def manually_trace_get_mal_node_msg():
    """手动追踪get_mal_node_msg函数的执行"""
    print("\n=== 手动追踪get_mal_node_msg函数 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    test_g = load_entity_level_dataset('theia', 'test', 0).to(device)
    
    cfg = Config()
    cfg.device = device
    cfg.n_dim = 5
    cfg.e_dim = 17
    cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)
    
    # 读取恶意节点
    with open('./data/theia/test_mal_node.json', 'r', encoding='utf-8') as f:
        mal_node_data = json.load(f)
    
    mal_node_ids = [int(x) for x in mal_node_data]
    valid_mal_nodes = [nid for nid in mal_node_ids if 0 <= nid < test_g.num_nodes()]
    
    print(f"有效恶意节点数: {len(valid_mal_nodes)}")
    
    # 手动分类恶意节点
    malicious_node = {'SUBJECT_PROCESS': [], 'FILE_OBJECT_BLOCK': [], 'NetFlowObject': []}
    
    for node_id in valid_mal_nodes:
        node_type_id = test_g.ndata['type'][node_id].item()
        
        # 根据类型ID分类
        if node_type_id == cfg.node_type_dict['SUBJECT_PROCESS']:
            malicious_node['SUBJECT_PROCESS'].append(node_id)
        elif node_type_id == cfg.node_type_dict['FILE_OBJECT_BLOCK']:
            malicious_node['FILE_OBJECT_BLOCK'].append(node_id)
        elif node_type_id == cfg.node_type_dict['NetFlowObject']:
            malicious_node['NetFlowObject'].append(node_id)
        else:
            # 这里可能是问题所在
            print(f"节点 {node_id} 的类型ID {node_type_id} 不在预期的恶意类型中")
            print(f"cfg.node_type_dict: {cfg.node_type_dict}")
            
            # 检查这个节点的实际类型名称
            with open('./data/theia/node_type_dict.json', 'r', encoding='utf-8') as f:
                node_type_dict = json.load(f)
            node_type_dict_reverse = {v: k for k, v in node_type_dict.items()}
            actual_type_name = node_type_dict_reverse.get(node_type_id, f"Unknown({node_type_id})")
            print(f"实际类型名称: {actual_type_name}")
            
            # 如果是MemoryObject，这可能解释了问题
            if actual_type_name == 'MemoryObject':
                print("⚠️  发现MemoryObject类型的恶意节点！")
                if 'MemoryObject' not in malicious_node:
                    malicious_node['MemoryObject'] = []
                malicious_node['MemoryObject'].append(node_id)
    
    print(f"\n手动分类结果:")
    for node_type, nodes in malicious_node.items():
        print(f"  {node_type}: {len(nodes)} 个节点")

def check_node_type_mapping_consistency():
    """检查节点类型映射的一致性"""
    print("\n=== 检查节点类型映射的一致性 ===")
    
    # 加载数据文件中的节点类型字典
    with open('./data/theia/node_type_dict.json', 'r', encoding='utf-8') as f:
        file_node_type_dict = json.load(f)
    
    # 获取配置中的节点类型字典
    cfg = Config()
    cfg_node_type_dict, _ = get_map(cfg.dataset)
    
    print(f"文件中的节点类型字典: {file_node_type_dict}")
    print(f"配置中的节点类型字典: {cfg_node_type_dict}")
    
    # 检查是否一致
    if file_node_type_dict == cfg_node_type_dict:
        print("✅ 节点类型字典一致")
    else:
        print("⚠️  节点类型字典不一致！")
        
        # 找出差异
        file_keys = set(file_node_type_dict.keys())
        cfg_keys = set(cfg_node_type_dict.keys())
        
        only_in_file = file_keys - cfg_keys
        only_in_cfg = cfg_keys - file_keys
        
        if only_in_file:
            print(f"只在文件中的类型: {only_in_file}")
        if only_in_cfg:
            print(f"只在配置中的类型: {only_in_cfg}")
        
        # 检查值的差异
        common_keys = file_keys & cfg_keys
        for key in common_keys:
            if file_node_type_dict[key] != cfg_node_type_dict[key]:
                print(f"类型 {key}: 文件中={file_node_type_dict[key]}, 配置中={cfg_node_type_dict[key]}")

def main():
    """主函数"""
    print("恶意节点类型标注检查")
    print("=" * 50)
    
    # 检查恶意节点分类逻辑
    check_malicious_node_classification()
    
    # 分析get_mal_node_msg函数的逻辑
    analyze_get_mal_node_msg_logic()
    
    # 手动追踪函数执行
    manually_trace_get_mal_node_msg()
    
    # 检查节点类型映射的一致性
    check_node_type_mapping_consistency()
    
    print("\n" + "=" * 50)
    print("检查完成!")

if __name__ == "__main__":
    main()
