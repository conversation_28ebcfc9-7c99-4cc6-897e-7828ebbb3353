#!/usr/bin/env python3
"""
测试训练脚本
"""

import torch
import warnings
from utils.config import build_args
from utils.loaddata import load_metadata
warnings.filterwarnings('ignore')

def test_basic_loading():
    """测试基本加载功能"""
    print("=== 测试基本加载功能 ===")
    
    try:
        # 测试设备
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"使用设备: {device}")
        
        # 测试参数加载
        args = build_args()
        print(f"参数加载成功: {args.dataset}")
        
        # 测试元数据加载
        metadata = load_metadata(args.dataset)
        print(f"元数据加载成功:")
        print(f"  训练图数量: {metadata['n_train']}")
        print(f"  测试图数量: {metadata['n_test']}")
        print(f"  节点特征维度: {metadata['node_feature_dim']}")
        print(f"  边特征维度: {metadata['edge_feature_dim']}")
        
        return True
        
    except Exception as e:
        print(f"基本加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_loading():
    """测试模型加载"""
    print("\n=== 测试模型加载 ===")
    
    try:
        from model.autoencoder import build_model
        from utils.utils import create_optimizer
        
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        args = build_args()
        metadata = load_metadata(args.dataset)
        
        args.num_hidden = 64
        args.num_layers = 3
        args.n_dim = metadata['node_feature_dim']
        args.e_dim = metadata['edge_feature_dim']
        
        model = build_model(args)
        model = model.to(device)
        print(f"模型加载成功，设备: {next(model.parameters()).device}")
        
        optimizer = create_optimizer(args.optimizer, model, args.lr, args.weight_decay)
        print("优化器创建成功")
        
        return True
        
    except Exception as e:
        print(f"模型加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_poison_config():
    """测试投毒配置"""
    print("\n=== 测试投毒配置 ===")
    
    try:
        from poison_main import Config
        from darpatc import get_map, TriggerGenerator
        
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        metadata = load_metadata('theia')
        
        cfg = Config()
        cfg.device = device
        cfg.n_dim = metadata['node_feature_dim']
        cfg.e_dim = metadata['edge_feature_dim']
        cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)
        
        print(f"投毒配置成功:")
        print(f"  设备: {cfg.device}")
        print(f"  节点维度: {cfg.n_dim}")
        print(f"  边维度: {cfg.e_dim}")
        print(f"  触发器形状: {cfg.trigger_shape}")
        
        # 测试触发器生成器
        trigger_generator = TriggerGenerator(cfg.n_dim, cfg.e_dim, cfg.trigger_shape)
        trigger_generator = trigger_generator.to(device)
        print("触发器生成器创建成功")
        
        return True
        
    except Exception as e:
        print(f"投毒配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_loading():
    """测试数据加载"""
    print("\n=== 测试数据加载 ===")
    
    try:
        from utils.loaddata import load_entity_level_dataset
        
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 加载一个训练图
        g = load_entity_level_dataset('theia', 'train', 0).to(device)
        print(f"训练图加载成功:")
        print(f"  节点数: {g.num_nodes()}")
        print(f"  边数: {g.num_edges()}")
        print(f"  设备: {g.device}")
        
        return True
        
    except Exception as e:
        print(f"数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("训练脚本测试")
    print("=" * 50)
    
    tests = [
        ("基本加载", test_basic_loading),
        ("模型加载", test_model_loading),
        ("投毒配置", test_poison_config),
        ("数据加载", test_data_loading),
    ]
    
    results = []
    for name, test_func in tests:
        result = test_func()
        results.append((name, result))
    
    print("\n" + "=" * 50)
    print("测试结果:")
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
    
    all_passed = all(result for _, result in results)
    if all_passed:
        print("\n🎉 所有测试通过！可以运行完整训练")
    else:
        print("\n⚠️  有测试失败，请检查配置")

if __name__ == "__main__":
    main()
