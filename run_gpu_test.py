#!/usr/bin/env python3
"""
简单的GPU测试运行脚本
"""

import torch
import sys
import os

def check_environment():
    """检查运行环境"""
    print("=== 环境检查 ===")
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    
    if torch.cuda.is_available():
        print(f"CUDA可用: 是")
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU设备数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"GPU {i}: {torch.cuda.get_device_name(i)}")
    else:
        print("CUDA可用: 否")
        print("将使用CPU运行")
    
    return torch.cuda.is_available()

def test_basic_operations():
    """测试基本GPU操作"""
    print("\n=== 基本GPU操作测试 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 测试tensor创建和操作
    try:
        x = torch.randn(1000, 64, device=device)
        y = torch.randn(1000, 64, device=device)
        
        # 测试矩阵乘法
        z = torch.mm(x, y.T)
        print(f"矩阵乘法测试成功: {z.shape}")
        
        # 测试距离计算
        distances = torch.cdist(x[:100], y[:100], p=2)
        print(f"距离计算测试成功: {distances.shape}")
        
        # 测试topk
        topk_values, topk_indices = torch.topk(distances, k=5, dim=1, largest=False)
        print(f"TopK测试成功: {topk_values.shape}")
        
        if torch.cuda.is_available():
            print(f"GPU内存使用: {torch.cuda.memory_allocated()/1024**2:.1f}MB")
        
        print("基本操作测试通过!")
        return True
        
    except Exception as e:
        print(f"基本操作测试失败: {e}")
        return False

def test_config():
    """测试配置类"""
    print("\n=== 配置测试 ===")
    
    try:
        from poison_main import Config
        cfg = Config()
        
        print(f"设备: {cfg.device}")
        print(f"触发器批处理大小: {cfg.trigger_batch_size}")
        print(f"嵌入批处理大小: {cfg.embedding_batch_size}")
        print(f"触发器形状: {cfg.trigger_shape}")
        
        print("配置测试通过!")
        return True
        
    except Exception as e:
        print(f"配置测试失败: {e}")
        return False

def test_imports():
    """测试关键模块导入"""
    print("\n=== 导入测试 ===")
    
    modules_to_test = [
        'poison_main',
        'attack',
        'attack_utils', 
        'darpatc'
    ]
    
    success_count = 0
    for module in modules_to_test:
        try:
            __import__(module)
            print(f"✓ {module}")
            success_count += 1
        except Exception as e:
            print(f"✗ {module}: {e}")
    
    print(f"导入测试: {success_count}/{len(modules_to_test)} 成功")
    return success_count == len(modules_to_test)

def main():
    """主函数"""
    print("GPU优化代码测试")
    print("=" * 50)
    
    # 检查环境
    cuda_available = check_environment()
    
    # 测试导入
    import_success = test_imports()
    if not import_success:
        print("\n❌ 导入测试失败，请检查代码")
        return
    
    # 测试配置
    config_success = test_config()
    if not config_success:
        print("\n❌ 配置测试失败")
        return
    
    # 测试基本操作
    basic_success = test_basic_operations()
    if not basic_success:
        print("\n❌ 基本操作测试失败")
        return
    
    print("\n" + "=" * 50)
    if cuda_available:
        print("✅ 所有测试通过! GPU优化代码可以运行")
        print("\n下一步:")
        print("1. 运行 'python test_gpu_optimization.py' 进行详细测试")
        print("2. 运行 'python poison_main.py' 开始训练")
    else:
        print("⚠️  测试通过，但CUDA不可用，将使用CPU运行")
        print("建议安装CUDA版本的PyTorch以获得更好性能")

if __name__ == "__main__":
    main()
