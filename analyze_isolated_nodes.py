#!/usr/bin/env python3
"""
分析孤立节点问题
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import json
from collections import Counter

from utils.config import build_args
from utils.loaddata import load_metadata, load_entity_level_dataset
from model.autoencoder import build_model
from poison_main import Config
from attack_utils import get_map, get_mal_node_msg

def analyze_isolated_nodes():
    """分析孤立节点问题"""
    print("=== 分析孤立节点问题 ===")
    
    # 设备设置
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 加载参数和元数据
    args = build_args()
    dataset_name = args.dataset
    metadata = load_metadata(dataset_name)
    
    # 加载节点类型字典
    with open(f'./data/{dataset_name}/node_type_dict.json', 'r', encoding='utf-8') as f:
        node_type_dict = json.load(f)
    node_type_dict_reverse = {v: k for k, v in node_type_dict.items()}
    
    # 加载测试图
    g = load_entity_level_dataset(dataset_name, 'test', 0).to(device)
    print(f"测试图: {g.num_nodes()} 节点, {g.num_edges()} 边")
    
    # 获取恶意节点
    cfg = Config()
    cfg.device = device
    cfg.n_dim = metadata['node_feature_dim']
    cfg.e_dim = metadata['edge_feature_dim']
    cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)
    malicious_node, mal_file_msg, mal_socket_msg = get_mal_node_msg(cfg, g)
    
    # 统计恶意节点
    all_mal_nodes = []
    for node_type, nodes in malicious_node.items():
        all_mal_nodes.extend(nodes)
        print(f"{node_type}: {len(nodes)} 个恶意节点")
    
    print(f"恶意节点总数: {len(all_mal_nodes)}")
    
    # 分析所有节点的连接情况
    print("\n=== 分析节点连接情况 ===")
    
    # 计算每个节点的度
    node_degrees = {}
    for i in range(g.num_nodes()):
        node_degrees[i] = 0
    
    # 统计度
    for src, dst in zip(*g.edges()):
        src_id = src.item()
        dst_id = dst.item()
        node_degrees[src_id] += 1
        node_degrees[dst_id] += 1
    
    # 找出孤立节点
    isolated_nodes = [node for node, degree in node_degrees.items() if degree == 0]
    print(f"孤立节点总数: {len(isolated_nodes)}")
    print(f"孤立节点比例: {len(isolated_nodes)/g.num_nodes()*100:.2f}%")
    
    # 分析恶意节点中的孤立节点
    isolated_mal_nodes = [node for node in all_mal_nodes if node in isolated_nodes]
    print(f"孤立的恶意节点数: {len(isolated_mal_nodes)}")
    print(f"恶意节点中孤立节点比例: {len(isolated_mal_nodes)/len(all_mal_nodes)*100:.2f}%")
    
    # 按类型分析孤立的恶意节点
    print("\n按类型分析孤立的恶意节点:")
    for node_type, nodes in malicious_node.items():
        isolated_count = sum(1 for node in nodes if node in isolated_nodes)
        print(f"  {node_type}: {isolated_count}/{len(nodes)} ({isolated_count/len(nodes)*100:.1f}%)")
    
    # 分析孤立节点的类型分布
    print("\n=== 孤立节点类型分布 ===")
    isolated_types = g.ndata['type'][isolated_nodes].cpu().numpy()
    isolated_type_counts = {}
    for t in isolated_types:
        type_name = node_type_dict_reverse.get(t, "Unknown")
        isolated_type_counts[type_name] = isolated_type_counts.get(type_name, 0) + 1
    
    print("孤立节点类型统计:")
    for t, count in sorted(isolated_type_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"  {t}: {count} ({count/len(isolated_nodes)*100:.1f}%)")
    
    # 分析所有节点的类型分布
    print("\n=== 所有节点类型分布 ===")
    all_types = g.ndata['type'].cpu().numpy()
    all_type_counts = {}
    for t in all_types:
        type_name = node_type_dict_reverse.get(t, "Unknown")
        all_type_counts[type_name] = all_type_counts.get(type_name, 0) + 1
    
    print("所有节点类型统计:")
    for t, count in sorted(all_type_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"  {t}: {count} ({count/g.num_nodes()*100:.1f}%)")
    
    # 分析度分布
    print("\n=== 度分布分析 ===")
    degrees = list(node_degrees.values())
    print(f"平均度: {np.mean(degrees):.2f}")
    print(f"度中位数: {np.median(degrees):.2f}")
    print(f"最大度: {np.max(degrees)}")
    print(f"最小度: {np.min(degrees)}")
    
    # 统计度分布
    degree_counts = Counter(degrees)
    print("\n度分布统计 (前10):")
    for degree, count in degree_counts.most_common(10):
        print(f"  度 {degree}: {count} 个节点 ({count/g.num_nodes()*100:.1f}%)")
    
    # 可视化度分布
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    plt.hist(degrees, bins=50, alpha=0.7)
    plt.xlabel('节点度')
    plt.ylabel('频率')
    plt.title('节点度分布')
    plt.yscale('log')
    
    plt.subplot(2, 2, 2)
    # 恶意节点度分布
    mal_degrees = [node_degrees[node] for node in all_mal_nodes]
    benign_degrees = [node_degrees[node] for node in range(g.num_nodes()) if node not in all_mal_nodes]
    
    plt.hist(benign_degrees, bins=30, alpha=0.5, label='良性节点', density=True)
    plt.hist(mal_degrees, bins=30, alpha=0.5, label='恶意节点', density=True)
    plt.xlabel('节点度')
    plt.ylabel('密度')
    plt.title('恶意vs良性节点度分布')
    plt.legend()
    plt.yscale('log')
    
    plt.subplot(2, 2, 3)
    # 按类型分析恶意节点度分布
    for node_type, nodes in malicious_node.items():
        if nodes:
            type_degrees = [node_degrees[node] for node in nodes]
            plt.hist(type_degrees, bins=20, alpha=0.5, label=node_type, density=True)
    plt.xlabel('节点度')
    plt.ylabel('密度')
    plt.title('不同类型恶意节点度分布')
    plt.legend()
    
    plt.subplot(2, 2, 4)
    # 孤立节点类型饼图
    type_names = list(isolated_type_counts.keys())
    type_counts = list(isolated_type_counts.values())
    plt.pie(type_counts, labels=type_names, autopct='%1.1f%%')
    plt.title('孤立节点类型分布')
    
    plt.tight_layout()
    plt.savefig('isolated_nodes_analysis.png', dpi=300, bbox_inches='tight')
    print("孤立节点分析图已保存到 isolated_nodes_analysis.png")
    
    return {
        'isolated_nodes': isolated_nodes,
        'isolated_mal_nodes': isolated_mal_nodes,
        'node_degrees': node_degrees,
        'malicious_node': malicious_node,
        'all_mal_nodes': all_mal_nodes
    }

def analyze_graph_structure():
    """分析图结构问题"""
    print("\n=== 分析图结构问题 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    dataset_name = 'theia'
    
    # 加载测试图
    g = load_entity_level_dataset(dataset_name, 'test', 0).to(device)
    
    # 检查图的基本属性
    print(f"图是否为有向图: {g.is_directed()}")
    print(f"图是否有自环: {g.has_self_loop()}")
    print(f"图是否有多重边: {g.is_multigraph()}")
    
    # 检查边的分布
    src, dst = g.edges()
    print(f"边数: {len(src)}")
    print(f"唯一源节点数: {len(torch.unique(src))}")
    print(f"唯一目标节点数: {len(torch.unique(dst))}")
    
    # 检查节点ID的连续性
    node_ids = torch.arange(g.num_nodes())
    print(f"节点ID范围: {node_ids.min().item()} - {node_ids.max().item()}")
    
    # 检查边的有效性
    max_node_id = g.num_nodes() - 1
    invalid_edges = ((src > max_node_id) | (dst > max_node_id)).sum()
    print(f"无效边数: {invalid_edges}")
    
    # 分析连通性
    print("\n分析图连通性...")
    
    # 转换为无向图进行连通性分析
    import networkx as nx
    
    # 创建NetworkX图
    G = nx.Graph()
    G.add_nodes_from(range(g.num_nodes()))
    
    # 添加边
    edges = [(src[i].item(), dst[i].item()) for i in range(len(src))]
    G.add_edges_from(edges)
    
    # 分析连通组件
    connected_components = list(nx.connected_components(G))
    print(f"连通组件数: {len(connected_components)}")
    
    if len(connected_components) > 1:
        component_sizes = [len(comp) for comp in connected_components]
        print(f"最大连通组件大小: {max(component_sizes)}")
        print(f"最小连通组件大小: {min(component_sizes)}")
        print(f"平均连通组件大小: {np.mean(component_sizes):.2f}")
        
        # 找出大的连通组件
        large_components = [comp for comp in connected_components if len(comp) > 100]
        print(f"大连通组件数 (>100节点): {len(large_components)}")
        
        # 分析孤立节点所在的连通组件
        isolated_nodes = [node for node in G.nodes() if G.degree(node) == 0]
        print(f"NetworkX计算的孤立节点数: {len(isolated_nodes)}")

def check_malicious_node_selection():
    """检查恶意节点选择逻辑"""
    print("\n=== 检查恶意节点选择逻辑 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    g = load_entity_level_dataset('theia', 'test', 0).to(device)
    
    cfg = Config()
    cfg.device = device
    metadata = load_metadata(cfg.dataset)
    cfg.n_dim = metadata['node_feature_dim']
    cfg.e_dim = metadata['edge_feature_dim']
    cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)
    
    print("检查恶意节点选择过程...")
    
    # 手动执行get_mal_node_msg的逻辑
    with open('./data/theia/test_mal_node.json', 'r', encoding='utf-8') as f:
        mal_node_data = json.load(f)
    
    print(f"从JSON文件读取的恶意节点数据: {len(mal_node_data)} 个条目")
    
    # 检查这些节点是否在图中存在
    max_node_id = g.num_nodes() - 1
    valid_nodes = 0
    invalid_nodes = 0
    
    for node_id in mal_node_data:
        if isinstance(node_id, (int, str)):
            node_id = int(node_id)
            if 0 <= node_id <= max_node_id:
                valid_nodes += 1
            else:
                invalid_nodes += 1
                print(f"无效节点ID: {node_id} (超出范围 0-{max_node_id})")
    
    print(f"有效恶意节点: {valid_nodes}")
    print(f"无效恶意节点: {invalid_nodes}")

def main():
    """主函数"""
    print("孤立节点问题分析")
    print("=" * 50)
    
    # 分析孤立节点
    isolated_data = analyze_isolated_nodes()
    
    # 分析图结构
    analyze_graph_structure()
    
    # 检查恶意节点选择
    check_malicious_node_selection()
    
    print("\n" + "=" * 50)
    print("分析完成!")
    
    print("\n🔍 关键发现:")
    print(f"1. 孤立恶意节点数: {len(isolated_data['isolated_mal_nodes'])}")
    print(f"2. 恶意节点总数: {len(isolated_data['all_mal_nodes'])}")
    print(f"3. 孤立比例: {len(isolated_data['isolated_mal_nodes'])/len(isolated_data['all_mal_nodes'])*100:.1f}%")
    
    if len(isolated_data['isolated_mal_nodes']) > 0:
        print("\n⚠️  问题诊断:")
        print("大量恶意节点是孤立的，这解释了为什么后门攻击效果不佳:")
        print("- 孤立节点没有邻居，无法通过图结构学习有意义的表示")
        print("- 孤立节点的嵌入主要依赖于节点特征，而不是图结构")
        print("- 这导致恶意节点和良性节点的嵌入可能非常相似")
        
        print("\n💡 建议解决方案:")
        print("1. 检查数据预处理过程，确保边信息正确加载")
        print("2. 重新选择有连接的恶意节点")
        print("3. 或者修改图构建逻辑，为孤立节点添加合理的连接")

if __name__ == "__main__":
    main()
