#!/usr/bin/env python3
"""
测试评估脚本
"""

import torch
import numpy as np
from utils.config import build_args
from utils.loaddata import load_metadata, load_entity_level_dataset
from model.autoencoder import build_model

def test_eval_basic():
    """测试基本评估功能"""
    print("=== 测试基本评估功能 ===")
    
    try:
        # 设备设置
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"使用设备: {device}")
        
        # 加载参数和元数据
        args = build_args()
        metadata = load_metadata(args.dataset)
        
        args.n_dim = metadata['node_feature_dim']
        args.e_dim = metadata['edge_feature_dim']
        args.num_hidden = 64
        args.num_layers = 3
        
        print(f"数据集: {args.dataset}")
        print(f"测试图数量: {metadata['n_test']}")
        
        # 加载模型
        model = build_model(args)
        model.load_state_dict(torch.load(f"./checkpoints/checkpoint-{args.dataset}.pt", map_location=device))
        model = model.to(device)
        model.eval()
        print("模型加载成功")
        
        # 测试单个图的嵌入
        g = load_entity_level_dataset(args.dataset, 'test', 0).to(device)
        print(f"测试图: {g.num_nodes()} 节点, {g.num_edges()} 边")
        
        with torch.no_grad():
            embeddings = model.embed(g)
            print(f"嵌入形状: {embeddings.shape}")
            print(f"嵌入设备: {embeddings.device}")
            
            # 转换为numpy
            embeddings_np = embeddings.cpu().numpy()
            print(f"转换为numpy成功: {embeddings_np.shape}")
            
            # 测试节点类型
            node_types = g.ndata['type'].cpu().numpy()
            print(f"节点类型形状: {node_types.shape}")
        
        return True
        
    except Exception as e:
        print(f"基本评估测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_poison_eval():
    """测试投毒评估"""
    print("\n=== 测试投毒评估 ===")
    
    try:
        from poison_main import Config
        from attack_utils import get_map, get_mal_node_msg
        from attack import poison_data
        from darpatc import TriggerGenerator
        
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 加载配置
        cfg = Config()
        cfg.device = device
        metadata = load_metadata(cfg.dataset)
        cfg.n_dim = metadata['node_feature_dim']
        cfg.e_dim = metadata['edge_feature_dim']
        cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)
        
        # 加载测试图
        g = load_entity_level_dataset(cfg.dataset, 'test', 0).to(device)
        print(f"原始图: {g.num_nodes()} 节点")
        
        # 获取恶意节点
        malicious_node, mal_file_msg, mal_socket_msg = get_mal_node_msg(cfg, g)
        print(f"恶意节点数量: {len(malicious_node['SUBJECT_PROCESS'])}")
        
        # 加载触发器生成器
        trigger_generator = TriggerGenerator(cfg.n_dim, cfg.e_dim, cfg.trigger_shape)
        try:
            trigger_generator.load_state_dict(torch.load('./poison_model/trigger_generator48.pth', map_location=device))
            print("触发器生成器加载成功")
        except FileNotFoundError:
            print("触发器生成器文件不存在，跳过投毒测试")
            return True
        
        trigger_generator = trigger_generator.to(device)
        trigger_generator.eval()
        
        # 生成投毒图
        g_p = poison_data(cfg, trigger_generator, g, malicious_node['SUBJECT_PROCESS'], mal_socket_msg, mal_file_msg)
        print(f"投毒后图: {g_p.num_nodes()} 节点")
        
        return True
        
    except Exception as e:
        print(f"投毒评估测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("评估脚本测试")
    print("=" * 50)
    
    tests = [
        ("基本评估", test_eval_basic),
        ("投毒评估", test_poison_eval),
    ]
    
    results = []
    for name, test_func in tests:
        result = test_func()
        results.append((name, result))
    
    print("\n" + "=" * 50)
    print("测试结果:")
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")

if __name__ == "__main__":
    main()
