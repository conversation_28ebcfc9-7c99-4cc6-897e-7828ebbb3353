#!/usr/bin/env python3
"""
分析分离损失问题 - 为什么干净恶意节点与良性节点变得相似
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from sklearn.neighbors import NearestNeighbors
from sklearn.manifold import TSNE
import json

from utils.config import build_args
from utils.loaddata import load_metadata, load_entity_level_dataset
from model.autoencoder import build_model
from poison_main import Config
from attack_utils import get_map, get_mal_node_msg, cal_loss_g
from attack import poison_data
from darpatc import TriggerGenerator

def analyze_training_vs_test_distribution():
    """分析训练集和测试集的分布差异"""
    print("=== 分析训练集和测试集的分布差异 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 加载模型
    args = build_args()
    metadata = load_metadata(args.dataset)
    args.n_dim = metadata['node_feature_dim']
    args.e_dim = metadata['edge_feature_dim']
    args.num_hidden = 64
    args.num_layers = 3
    
    model = build_model(args)
    model.load_state_dict(torch.load(f"./checkpoints/checkpoint-{args.dataset}.pt", map_location=device))
    model = model.to(device)
    model.eval()
    
    # 加载训练数据（投毒后的）
    train_embeddings_all = []
    train_node_types_all = []
    
    print("加载训练数据嵌入...")
    for i in range(metadata['n_train']):
        # 加载投毒后的训练数据
        import pickle as pkl
        with open(f'./data/theia/train{i}_poisoned.pkl', 'rb') as f:
            train_g = pkl.load(f).to(device)
        
        with torch.no_grad():
            train_emb = model.embed(train_g).cpu().numpy()
            train_types = train_g.ndata['type'].cpu().numpy()
        
        train_embeddings_all.append(train_emb)
        train_node_types_all.append(train_types)
    
    train_embeddings = np.concatenate(train_embeddings_all, axis=0)
    train_node_types = np.concatenate(train_node_types_all, axis=0)
    
    # 加载测试数据
    test_g = load_entity_level_dataset('theia', 'test', 0).to(device)
    
    with torch.no_grad():
        test_embeddings = model.embed(test_g).cpu().numpy()
        test_node_types = test_g.ndata['type'].cpu().numpy()
    
    # 获取恶意节点
    cfg = Config()
    cfg.device = device
    cfg.n_dim = metadata['node_feature_dim']
    cfg.e_dim = metadata['edge_feature_dim']
    cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)
    malicious_node, mal_file_msg, mal_socket_msg = get_mal_node_msg(cfg, test_g)
    
    # 获取所有恶意节点索引
    all_mal_nodes = []
    for nodes in malicious_node.values():
        all_mal_nodes.extend(nodes)
    
    print(f"训练嵌入: {train_embeddings.shape}")
    print(f"测试嵌入: {test_embeddings.shape}")
    print(f"恶意节点数: {len(all_mal_nodes)}")
    
    # 分析不同类型节点的分布
    analyze_node_type_distributions(train_embeddings, train_node_types, 
                                   test_embeddings, test_node_types, 
                                   all_mal_nodes, cfg)

def analyze_node_type_distributions(train_embeddings, train_node_types, 
                                   test_embeddings, test_node_types, 
                                   mal_nodes, cfg):
    """分析不同类型节点的分布"""
    print("\n=== 分析不同类型节点的分布 ===")
    
    # 节点类型映射
    type_names = {0: 'SUBJECT_PROCESS', 1: 'FILE_OBJECT_BLOCK', 2: 'NetFlowObject', 3: 'MemoryObject', 4: 'PRINCIPAL_LOCAL'}
    
    # 分析每种类型的节点
    for type_id, type_name in type_names.items():
        print(f"\n--- {type_name} ---")
        
        # 训练集中该类型的节点
        train_mask = train_node_types == type_id
        train_type_emb = train_embeddings[train_mask]
        
        # 测试集中该类型的节点
        test_mask = test_node_types == type_id
        test_type_emb = test_embeddings[test_mask]
        
        # 测试集中该类型的恶意节点
        mal_type_nodes = [node for node in mal_nodes if test_node_types[node] == type_id]
        
        print(f"训练集 {type_name}: {len(train_type_emb)} 个节点")
        print(f"测试集 {type_name}: {len(test_type_emb)} 个节点")
        print(f"恶意 {type_name}: {len(mal_type_nodes)} 个节点")
        
        if len(train_type_emb) > 0 and len(mal_type_nodes) > 0:
            mal_type_emb = test_embeddings[mal_type_nodes]
            
            # 计算距离
            if len(train_type_emb) > 10000:  # 如果训练数据太多，随机采样
                sample_indices = np.random.choice(len(train_type_emb), 10000, replace=False)
                train_type_emb_sample = train_type_emb[sample_indices]
            else:
                train_type_emb_sample = train_type_emb
            
            # 标准化
            train_mean = train_type_emb_sample.mean(axis=0)
            train_std = train_type_emb_sample.std(axis=0)
            train_norm = (train_type_emb_sample - train_mean) / train_std
            mal_norm = (mal_type_emb - train_mean) / train_std
            
            # 计算最近邻距离
            nbrs = NearestNeighbors(n_neighbors=min(10, len(train_norm)), n_jobs=-1)
            nbrs.fit(train_norm)
            distances, indices = nbrs.kneighbors(mal_norm)
            
            avg_distance = distances.mean()
            print(f"恶意节点到训练集的平均距离: {avg_distance:.4f}")
            
            # 如果距离很小，说明恶意节点与训练集很相似
            if avg_distance < 0.5:
                print(f"⚠️  {type_name} 恶意节点与训练集过于相似！")
                
                # 分析最近邻
                print("分析最近邻...")
                for i, (dist_row, idx_row) in enumerate(zip(distances[:3], indices[:3])):  # 只看前3个恶意节点
                    print(f"  恶意节点 {i}: 最近邻距离 = {dist_row[:3]}")

def analyze_trigger_impact_on_training():
    """分析触发器对训练数据的影响"""
    print("\n=== 分析触发器对训练数据的影响 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    metadata = load_metadata('theia')
    
    # 比较干净和投毒的训练数据
    import pickle as pkl
    
    for i in range(min(2, metadata['n_train'])):  # 只分析前2个训练图
        print(f"\n--- 训练图 {i} ---")
        
        # 加载干净和投毒的训练图
        with open(f'./data/theia/train{i}.pkl', 'rb') as f:
            clean_g = pkl.load(f)
        with open(f'./data/theia/train{i}_poisoned.pkl', 'rb') as f:
            poisoned_g = pkl.load(f)
        
        print(f"干净图: {clean_g.num_nodes()} 节点, {clean_g.num_edges()} 边")
        print(f"投毒图: {poisoned_g.num_nodes()} 节点, {poisoned_g.num_edges()} 边")
        
        # 分析新增的节点类型
        new_nodes = poisoned_g.num_nodes() - clean_g.num_nodes()
        if new_nodes > 0:
            new_node_types = poisoned_g.ndata['type'][-new_nodes:].cpu().numpy()
            type_counts = {}
            for t in new_node_types:
                type_counts[t] = type_counts.get(t, 0) + 1
            
            print(f"新增节点类型分布:")
            type_names = {0: 'SUBJECT_PROCESS', 1: 'FILE_OBJECT_BLOCK', 2: 'NetFlowObject', 3: 'MemoryObject', 4: 'PRINCIPAL_LOCAL'}
            for t, count in type_counts.items():
                type_name = type_names.get(t, f"Unknown({t})")
                print(f"  {type_name}: {count} 个")
        
        # 检查是否有trigger_mask
        if 'trigger_mask' in poisoned_g.ndata:
            trigger_nodes = (poisoned_g.ndata['trigger_mask'] > 0).sum().item()
            print(f"带触发器的节点数: {trigger_nodes}")

def visualize_embedding_clusters():
    """可视化嵌入聚类"""
    print("\n=== 可视化嵌入聚类 ===")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 加载模型和数据
    args = build_args()
    metadata = load_metadata(args.dataset)
    args.n_dim = metadata['node_feature_dim']
    args.e_dim = metadata['edge_feature_dim']
    args.num_hidden = 64
    args.num_layers = 3
    
    model = build_model(args)
    model.load_state_dict(torch.load(f"./checkpoints/checkpoint-{args.dataset}.pt", map_location=device))
    model = model.to(device)
    model.eval()
    
    # 加载测试数据
    test_g = load_entity_level_dataset('theia', 'test', 0).to(device)
    
    with torch.no_grad():
        test_embeddings = model.embed(test_g).cpu().numpy()
    
    # 获取恶意节点
    cfg = Config()
    cfg.device = device
    cfg.n_dim = metadata['node_feature_dim']
    cfg.e_dim = metadata['edge_feature_dim']
    cfg.node_type_dict, cfg.edge_type_dict = get_map(cfg.dataset)
    malicious_node, mal_file_msg, mal_socket_msg = get_mal_node_msg(cfg, test_g)
    
    all_mal_nodes = []
    for nodes in malicious_node.values():
        all_mal_nodes.extend(nodes)
    
    # 随机采样一些良性节点
    all_nodes = list(range(test_g.num_nodes()))
    benign_nodes = [n for n in all_nodes if n not in all_mal_nodes]
    np.random.seed(42)
    sampled_benign = np.random.choice(benign_nodes, min(5000, len(benign_nodes)), replace=False)
    
    # 合并数据进行降维
    combined_embeddings = np.vstack([
        test_embeddings[all_mal_nodes],
        test_embeddings[sampled_benign]
    ])
    
    # 使用t-SNE降维
    print("执行t-SNE降维...")
    tsne = TSNE(n_components=2, random_state=42, perplexity=30)
    combined_tsne = tsne.fit_transform(combined_embeddings)
    
    # 分离结果
    mal_tsne = combined_tsne[:len(all_mal_nodes)]
    benign_tsne = combined_tsne[len(all_mal_nodes):]
    
    # 绘制结果
    plt.figure(figsize=(12, 8))
    plt.scatter(benign_tsne[:, 0], benign_tsne[:, 1], c='blue', alpha=0.5, s=5, label='良性节点')
    plt.scatter(mal_tsne[:, 0], mal_tsne[:, 1], c='red', s=20, label='恶意节点')
    plt.title('测试集节点嵌入t-SNE可视化')
    plt.legend()
    plt.savefig('malicious_vs_benign_tsne.png', dpi=300, bbox_inches='tight')
    print("t-SNE可视化已保存到 malicious_vs_benign_tsne.png")
    
    # 分析聚类重叠
    from scipy.spatial.distance import cdist
    
    # 计算恶意节点与良性节点的最小距离
    distances = cdist(mal_tsne, benign_tsne, metric='euclidean')
    min_distances = distances.min(axis=1)
    
    print(f"恶意节点到最近良性节点的平均距离: {min_distances.mean():.4f}")
    print(f"距离小于1.0的恶意节点比例: {(min_distances < 1.0).mean()*100:.1f}%")
    
    if (min_distances < 1.0).mean() > 0.5:
        print("⚠️  超过50%的恶意节点与良性节点在嵌入空间中非常接近！")
        print("这解释了为什么分离损失为负数")

def main():
    """主函数"""
    print("分离损失问题分析")
    print("=" * 50)
    
    # 分析训练集和测试集的分布差异
    analyze_training_vs_test_distribution()
    
    # 分析触发器对训练数据的影响
    analyze_trigger_impact_on_training()
    
    # 可视化嵌入聚类
    visualize_embedding_clusters()
    
    print("\n" + "=" * 50)
    print("分析完成!")
    
    print("\n🔍 可能的问题原因:")
    print("1. 训练集中添加的触发器节点改变了整体数据分布")
    print("2. 模型学习到了错误的'正常'模式")
    print("3. 恶意节点的特征与训练集中的某些模式过于相似")
    print("4. 触发器的设计可能不够有效")
    
    print("\n💡 建议解决方案:")
    print("1. 检查触发器添加的逻辑，确保触发器足够独特")
    print("2. 调整触发器的形状和强度")
    print("3. 重新设计分离损失的计算方式")
    print("4. 分析训练集中投毒节点的分布")

if __name__ == "__main__":
    main()
